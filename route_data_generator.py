#!/usr/bin/env python3
"""
Route Data Generator
生成路线信息的Excel文件，包含每个路线中各个站点的详细信息
"""

import pandas as pd
import numpy as np
import random
from typing import Dict, List, Tuple
import os

def generate_route_data(num_routes: int = 5, 
                       min_stops_per_route: int = 10, 
                       max_stops_per_route: int = 25,
                       area_size: Tuple[float, float] = (100.0, 100.0)) -> Dict[int, pd.DataFrame]:
    """
    生成路线数据
    
    Args:
        num_routes: 生成的路线数量
        min_stops_per_route: 每条路线最少站点数
        max_stops_per_route: 每条路线最多站点数
        area_size: 区域大小 (width, height)
    
    Returns:
        字典，键为route_id，值为包含该路线站点信息的DataFrame
    """
    
    # 设置随机种子以确保可重现性
    random.seed(42)
    np.random.seed(42)
    
    routes_data = {}
    global_stop_id = 0  # 全局站点ID，确保不重复
    
    for route_id in range(num_routes):
        # 生成该路线的站点数量
        num_stops = random.randint(min_stops_per_route, max_stops_per_route)
        
        route_stops = []
        
        for position in range(num_stops):
            # 生成随机坐标 (0-100范围内)
            x_coord = round(random.uniform(0, area_size[0]), 2)
            y_coord = round(random.uniform(0, area_size[1]), 2)
            
            # 生成旅行距离 (0.8km - 1.5km)
            # 最后一站到下一站距离为0（终点站）
            if position == num_stops - 1:
                travel_distance = 0.0
            else:
                travel_distance = round(random.uniform(0.8, 1.5), 2)
            
            # 计算能耗 (travel_distance * 1.75 kWh/km)
            energy_consumption = round(travel_distance * 1.75, 3)
            
            stop_info = {
                'route_id': route_id,
                'stop_id': global_stop_id,
                'position_on_route': position,
                'x_coordinate': x_coord,
                'y_coordinate': y_coord,
                'travel_distance': travel_distance,
                'energy_consumption': energy_consumption
            }
            
            route_stops.append(stop_info)
            global_stop_id += 1
        
        # 创建该路线的DataFrame
        routes_data[route_id] = pd.DataFrame(route_stops)
    
    return routes_data

def save_routes_to_excel(routes_data: Dict[int, pd.DataFrame], filename: str = "route_information.xlsx") -> None:
    """
    将路线数据保存为Excel文件，每个路线一个工作表
    
    Args:
        routes_data: 包含所有路线数据的字典
        filename: 输出文件名
    """
    
    # 创建Excel writer对象
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        
        # 为每条路线创建一个工作表
        for route_id, route_df in routes_data.items():
            sheet_name = f'Route_{route_id}'
            route_df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            # 获取工作表对象进行格式化
            worksheet = writer.sheets[sheet_name]
            
            # 设置列宽
            column_widths = {
                'A': 12,  # route_id
                'B': 12,  # stop_id
                'C': 18,  # position_on_route
                'D': 15,  # x_coordinate
                'E': 15,  # y_coordinate
                'F': 18,  # travel_distance
                'G': 20   # energy_consumption
            }
            
            for col, width in column_widths.items():
                worksheet.column_dimensions[col].width = width
        
        # 创建汇总统计表
        summary_data = create_summary_data(routes_data)
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name='Summary', index=False)
        
        # 设置汇总表格式
        summary_worksheet = writer.sheets['Summary']
        summary_worksheet.column_dimensions['A'].width = 25
        summary_worksheet.column_dimensions['B'].width = 15
        
        # 创建所有路线的合并数据表
        all_routes_data = []
        for route_df in routes_data.values():
            all_routes_data.extend(route_df.to_dict('records'))
        
        all_routes_df = pd.DataFrame(all_routes_data)
        all_routes_df.to_excel(writer, sheet_name='All_Routes', index=False)
        
        # 设置合并表格式
        all_routes_worksheet = writer.sheets['All_Routes']
        for col, width in column_widths.items():
            all_routes_worksheet.column_dimensions[col].width = width

def create_summary_data(routes_data: Dict[int, pd.DataFrame]) -> Dict:
    """
    创建汇总统计数据
    
    Args:
        routes_data: 包含所有路线数据的字典
    
    Returns:
        汇总统计数据字典
    """
    
    total_routes = len(routes_data)
    total_stops = sum(len(route_df) for route_df in routes_data.values())
    
    # 计算每条路线的总距离和总能耗
    route_distances = []
    route_energies = []
    route_stop_counts = []
    
    for route_df in routes_data.values():
        total_distance = route_df['travel_distance'].sum()
        total_energy = route_df['energy_consumption'].sum()
        stop_count = len(route_df)
        
        route_distances.append(total_distance)
        route_energies.append(total_energy)
        route_stop_counts.append(stop_count)
    
    summary_data = {
        '统计项目': [
            '总路线数量',
            '总站点数量',
            '平均每条路线站点数',
            '最短路线站点数',
            '最长路线站点数',
            '平均路线总距离 (km)',
            '最短路线距离 (km)',
            '最长路线距离 (km)',
            '平均路线总能耗 (kWh)',
            '最小路线能耗 (kWh)',
            '最大路线能耗 (kWh)'
        ],
        '数值': [
            total_routes,
            total_stops,
            round(np.mean(route_stop_counts), 1),
            min(route_stop_counts),
            max(route_stop_counts),
            round(np.mean(route_distances), 2),
            round(min(route_distances), 2),
            round(max(route_distances), 2),
            round(np.mean(route_energies), 3),
            round(min(route_energies), 3),
            round(max(route_energies), 3)
        ]
    }
    
    return summary_data

def create_custom_route_data() -> Dict[int, pd.DataFrame]:
    """
    创建自定义的路线数据（基于现有代码的示例）
    """
    
    # Route 0 数据
    route_0_data = [
        {'route_id': 0, 'stop_id': 0, 'position_on_route': 0, 'x_coordinate': 10.0, 'y_coordinate': 20.0, 'travel_distance': 1.2, 'energy_consumption': 2.1},
        {'route_id': 0, 'stop_id': 1, 'position_on_route': 1, 'x_coordinate': 15.5, 'y_coordinate': 25.8, 'travel_distance': 0.9, 'energy_consumption': 1.575},
        {'route_id': 0, 'stop_id': 2, 'position_on_route': 2, 'x_coordinate': 22.1, 'y_coordinate': 31.2, 'travel_distance': 1.1, 'energy_consumption': 1.925},
        {'route_id': 0, 'stop_id': 3, 'position_on_route': 3, 'x_coordinate': 28.7, 'y_coordinate': 35.6, 'travel_distance': 1.4, 'energy_consumption': 2.45},
        {'route_id': 0, 'stop_id': 4, 'position_on_route': 4, 'x_coordinate': 35.3, 'y_coordinate': 40.1, 'travel_distance': 0.0, 'energy_consumption': 0.0}
    ]
    
    # Route 1 数据
    route_1_data = [
        {'route_id': 1, 'stop_id': 5, 'position_on_route': 0, 'x_coordinate': 60.0, 'y_coordinate': 15.0, 'travel_distance': 1.0, 'energy_consumption': 1.75},
        {'route_id': 1, 'stop_id': 6, 'position_on_route': 1, 'x_coordinate': 65.2, 'y_coordinate': 20.3, 'travel_distance': 1.3, 'energy_consumption': 2.275},
        {'route_id': 1, 'stop_id': 7, 'position_on_route': 2, 'x_coordinate': 70.8, 'y_coordinate': 26.1, 'travel_distance': 0.8, 'energy_consumption': 1.4},
        {'route_id': 1, 'stop_id': 8, 'position_on_route': 3, 'x_coordinate': 75.5, 'y_coordinate': 30.7, 'travel_distance': 1.5, 'energy_consumption': 2.625},
        {'route_id': 1, 'stop_id': 9, 'position_on_route': 4, 'x_coordinate': 80.1, 'y_coordinate': 35.9, 'travel_distance': 1.2, 'energy_consumption': 2.1},
        {'route_id': 1, 'stop_id': 10, 'position_on_route': 5, 'x_coordinate': 85.7, 'y_coordinate': 41.2, 'travel_distance': 0.0, 'energy_consumption': 0.0}
    ]
    
    # Route 2 数据
    route_2_data = [
        {'route_id': 2, 'stop_id': 11, 'position_on_route': 0, 'x_coordinate': 5.0, 'y_coordinate': 80.0, 'travel_distance': 0.9, 'energy_consumption': 1.575},
        {'route_id': 2, 'stop_id': 12, 'position_on_route': 1, 'x_coordinate': 12.3, 'y_coordinate': 75.4, 'travel_distance': 1.1, 'energy_consumption': 1.925},
        {'route_id': 2, 'stop_id': 13, 'position_on_route': 2, 'x_coordinate': 18.9, 'y_coordinate': 70.2, 'travel_distance': 1.4, 'energy_consumption': 2.45},
        {'route_id': 2, 'stop_id': 14, 'position_on_route': 3, 'x_coordinate': 25.6, 'y_coordinate': 65.8, 'travel_distance': 0.0, 'energy_consumption': 0.0}
    ]
    
    routes_data = {
        0: pd.DataFrame(route_0_data),
        1: pd.DataFrame(route_1_data), 
        2: pd.DataFrame(route_2_data)
    }
    
    return routes_data

def main():
    """主函数"""
    
    print("🚌 电动公交路线数据生成器")
    print("=" * 50)
    
    # 选择生成模式
    mode = input("请选择生成模式 (1: 随机生成, 2: 使用示例数据): ").strip()
    
    if mode == "1":
        # 随机生成模式
        num_routes = int(input("请输入要生成的路线数量 (默认5): ") or "5")
        min_stops = int(input("请输入每条路线最少站点数 (默认5): ") or "5")
        max_stops = int(input("请输入每条路线最多站点数 (默认15): ") or "15")
        area_width = float(input("请输入区域宽度 (默认100.0): ") or "100.0")
        area_height = float(input("请输入区域高度 (默认100.0): ") or "100.0")
        
        print(f"\n正在生成 {num_routes} 条路线的随机数据...")
        routes_data = generate_route_data(num_routes, min_stops, max_stops, (area_width, area_height))
        filename = "route_information_random.xlsx"
        
    else:
        # 使用示例数据
        print("\n使用预设的示例数据...")
        routes_data = create_custom_route_data()
        filename = "route_information_example.xlsx"
    
    # 显示生成的数据
    print("\n生成的路线数据:")
    print("-" * 80)
    for route_id, route_df in routes_data.items():
        print(f"\nRoute {route_id}:")
        print(route_df.to_string(index=False))
    
    # 保存到Excel文件
    print(f"\n正在保存到 {filename}...")
    save_routes_to_excel(routes_data, filename)
    
    print("✅ Excel文件生成完成!")
    print(f"📁 文件位置: {os.path.abspath(filename)}")
    
    # 显示汇总信息
    total_routes = len(routes_data)
    total_stops = sum(len(route_df) for route_df in routes_data.values())
    
    print("\n📊 数据汇总:")
    print(f"  • 总路线数量: {total_routes}")
    print(f"  • 总站点数量: {total_stops}")
    print(f"  • 平均每条路线站点数: {total_stops/total_routes:.1f}")
    
    # 计算总距离和总能耗
    total_distance = sum(route_df['travel_distance'].sum() for route_df in routes_data.values())
    total_energy = sum(route_df['energy_consumption'].sum() for route_df in routes_data.values())
    
    print(f"  • 所有路线总距离: {total_distance:.2f} km")
    print(f"  • 所有路线总能耗: {total_energy:.3f} kWh")

if __name__ == "__main__":
    main()