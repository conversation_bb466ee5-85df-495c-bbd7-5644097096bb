import numpy as np
import pandas as pd
import random
import copy
import time
import os
from collections import defaultdict, deque
from dataclasses import dataclass, field
from typing import List, Dict, Tuple, Optional
from enum import Enum
from solution_logger import SolutionLogger

class OperatorEvaluator:
    """Enhanced evaluator with runtime tracking for destroy and repair operators"""
    
    def __init__(self):
        # Performance tracking for destroy operators
        self.destroy_stats = {
            'random_trip_removal': {
                'uses': 0, 'improvements': 0, 'total_improvement': 0, 'avg_improvement': 0,
                'total_runtime': 0.0, 'avg_runtime': 0.0, 'runtime_samples': []
            },
            'route_based_removal': {
                'uses': 0, 'improvements': 0, 'total_improvement': 0, 'avg_improvement': 0,
                'total_runtime': 0.0, 'avg_runtime': 0.0, 'runtime_samples': []
            },
            'time_based_removal': {
                'uses': 0, 'improvements': 0, 'total_improvement': 0, 'avg_improvement': 0,
                'total_runtime': 0.0, 'avg_runtime': 0.0, 'runtime_samples': []
            },
            'time_shift_removal': {
                'uses': 0, 'improvements': 0, 'total_improvement': 0, 'avg_improvement': 0,
                'total_runtime': 0.0, 'avg_runtime': 0.0, 'runtime_samples': []
            }
        }
        
        # Performance tracking for repair operators
        self.repair_stats = {
            'greedy_insertion': {
                'uses': 0, 'improvements': 0, 'total_improvement': 0, 'avg_improvement': 0, 
                'repair_efficiency': 0, 'total_runtime': 0.0, 'avg_runtime': 0.0, 'runtime_samples': []
            },
            'regret_insertion': {
                'uses': 0, 'improvements': 0, 'total_improvement': 0, 'avg_improvement': 0, 
                'repair_efficiency': 0, 'total_runtime': 0.0, 'avg_runtime': 0.0, 'runtime_samples': []
            },
            'time_aware_greedy_insertion': {
                'uses': 0, 'improvements': 0, 'total_improvement': 0, 'avg_improvement': 0, 
                'repair_efficiency': 0, 'total_runtime': 0.0, 'avg_runtime': 0.0, 'runtime_samples': []
            },
            'time_shift_insertion': {
                'uses': 0, 'improvements': 0, 'total_improvement': 0, 'avg_improvement': 0, 
                'repair_efficiency': 0, 'total_runtime': 0.0, 'avg_runtime': 0.0, 'runtime_samples': []
            }
        }
        
        # Combined operator performance (destroy + repair pairs)
        self.combo_stats = {}
        
        # Weight update parameters
        self.learning_rate = 0.1
        self.min_weight = 0.1
        self.weight_update_frequency = 50  # Update weights every N iterations
        self.iteration_count = 0
        
        # Runtime tracking settings
        self.max_runtime_samples = 50  # 保留最近50次运行时间样本
        
    def record_destroy_usage(self, operator_name: str, runtime: float = 0.0):
        """Record that a destroy operator was used with its runtime"""
        if operator_name in self.destroy_stats:
            stats = self.destroy_stats[operator_name]
            stats['uses'] += 1
            
            # Update runtime statistics
            if runtime > 0:
                stats['total_runtime'] += runtime
                stats['avg_runtime'] = stats['total_runtime'] / stats['uses']
                
                # Keep recent samples for variance calculation
                stats['runtime_samples'].append(runtime)
                if len(stats['runtime_samples']) > self.max_runtime_samples:
                    stats['runtime_samples'].pop(0)
    
    def record_repair_performance(self, operator_name: str, before_score: float, after_score: float, 
                                destroyed_score: float = None, runtime: float = 0.0):
        """Record repair operator performance including runtime"""
        if operator_name not in self.repair_stats:
            return
        
        # Calculate improvement more intelligently
        # If destroyed_score is much lower than before_score, the repair task is easier
        # If destroyed_score is close to before_score, the repair task is harder
        if destroyed_score is not None:
            destruction_impact = before_score - destroyed_score
            repair_improvement = after_score - destroyed_score
            
            # Adjust improvement based on destruction impact
            # If destruction was minimal, lower expectations for repair
            if destruction_impact < 10:  # Minimal destruction
                improvement = repair_improvement * 0.5  # Lower weight
            else:
                improvement = repair_improvement
        else:
            improvement = after_score - before_score
        
        stats = self.repair_stats[operator_name]
        stats['uses'] += 1
        stats['total_improvement'] += improvement
        
        if improvement > 0:
            stats['improvements'] += 1
            
        # Update average improvement
        stats['avg_improvement'] = stats['total_improvement'] / stats['uses']
        
        # Calculate repair efficiency (improvement per use)
        stats['repair_efficiency'] = stats['improvements'] / stats['uses'] if stats['uses'] > 0 else 0
        
        # Update runtime statistics
        if runtime > 0:
            stats['total_runtime'] += runtime
            stats['avg_runtime'] = stats['total_runtime'] / stats['uses']
            
            # Keep recent samples
            stats['runtime_samples'].append(runtime)
            if len(stats['runtime_samples']) > self.max_runtime_samples:
                stats['runtime_samples'].pop(0)
    
    def record_combo_performance(self, destroy_op: str, repair_op: str, improvement: float):
        """Record performance of destroy+repair operator combinations"""
        combo_key = f"{destroy_op}+{repair_op}"
        
        if combo_key not in self.combo_stats:
            self.combo_stats[combo_key] = {
                'uses': 0, 'total_improvement': 0, 'avg_improvement': 0, 'success_rate': 0
            }
        
        stats = self.combo_stats[combo_key]
        stats['uses'] += 1
        stats['total_improvement'] += improvement
        stats['avg_improvement'] = stats['total_improvement'] / stats['uses']
        
        # Calculate success rate (positive improvements)
        if improvement > 0:
            success_count = stats.get('successes', 0) + 1
            stats['successes'] = success_count
        else:
            stats['successes'] = stats.get('successes', 0)
            
        stats['success_rate'] = stats['successes'] / stats['uses']
    
    def get_destroy_operator_score(self, operator_name: str) -> float:
        """Calculate score for destroy operator based on its contribution to overall improvements"""
        if operator_name not in self.destroy_stats:
            return 1.0
            
        stats = self.destroy_stats[operator_name]
        if stats['uses'] == 0:
            return 1.0
        
        # Score based on average improvement from combinations involving this destroy operator
        relevant_combos = [combo for combo in self.combo_stats.keys() if combo.startswith(operator_name)]
        
        if not relevant_combos:
            return 1.0
            
        avg_combo_performance = sum(self.combo_stats[combo]['avg_improvement'] for combo in relevant_combos) / len(relevant_combos)
        
        # Normalize score (positive values get higher scores)
        base_score = 1.0
        performance_bonus = max(0, avg_combo_performance / 100)  # Scale improvement to reasonable bonus
        diversity_bonus = min(stats['uses'] / 10, 0.5)  # Bonus for being used (promotes diversity)
        
        return base_score + performance_bonus + diversity_bonus
    
    def get_repair_operator_score(self, operator_name: str) -> float:
        """Calculate score for repair operator based on its direct performance"""
        if operator_name not in self.repair_stats:
            return 1.0
            
        stats = self.repair_stats[operator_name]
        if stats['uses'] == 0:
            return 1.0
        
        # Score based on multiple factors
        efficiency_score = stats['repair_efficiency']  # Success rate
        improvement_score = max(0, stats['avg_improvement'] / 100)  # Average improvement
        reliability_score = min(stats['uses'] / 20, 1.0)  # Reliability from usage
        
        # Weighted combination
        total_score = (efficiency_score * 0.4 + 
                      improvement_score * 0.4 + 
                      reliability_score * 0.2)
        
        return max(self.min_weight, total_score)
    
    def update_weights(self, destroy_weights: dict, repair_weights: dict) -> tuple:
        """Update operator weights based on performance"""
        self.iteration_count += 1
        
        if self.iteration_count % self.weight_update_frequency != 0:
            return destroy_weights, repair_weights
        
        # Update destroy operator weights
        new_destroy_weights = {}
        for op_name in destroy_weights:
            current_weight = destroy_weights[op_name]
            performance_score = self.get_destroy_operator_score(op_name)
            
            # Adaptive weight update with learning rate
            new_weight = current_weight + self.learning_rate * (performance_score - current_weight)
            new_destroy_weights[op_name] = max(self.min_weight, new_weight)
        
        # Update repair operator weights  
        new_repair_weights = {}
        for op_name in repair_weights:
            current_weight = repair_weights[op_name]
            performance_score = self.get_repair_operator_score(op_name)
            
            new_weight = current_weight + self.learning_rate * (performance_score - current_weight)
            new_repair_weights[op_name] = max(self.min_weight, new_weight)
        
        return new_destroy_weights, new_repair_weights
    
    def get_runtime_statistics(self, operator_name: str, operator_type: str = 'repair') -> Dict:
        """Get detailed runtime statistics for an operator"""
        if operator_type == 'repair' and operator_name in self.repair_stats:
            stats = self.repair_stats[operator_name]
        elif operator_type == 'destroy' and operator_name in self.destroy_stats:
            stats = self.destroy_stats[operator_name]
        else:
            return {'avg_runtime': 0.0, 'std_runtime': 0.0, 'min_runtime': 0.0, 'max_runtime': 0.0}
        
        samples = stats['runtime_samples']
        if not samples:
            return {'avg_runtime': 0.0, 'std_runtime': 0.0, 'min_runtime': 0.0, 'max_runtime': 0.0}
        
        return {
            'avg_runtime': np.mean(samples),
            'std_runtime': np.std(samples),
            'min_runtime': np.min(samples),
            'max_runtime': np.max(samples)
        }
    
    def print_performance_report(self):
        """Print enhanced performance report with runtime statistics"""
        print("\n" + "="*80)
        print("ENHANCED OPERATOR PERFORMANCE REPORT")
        print("="*80)
        
        # Destroy operators
        print("\n🔨 DESTROY OPERATORS:")
        print(f"{'Operator':<25} {'Uses':<6} {'Score':<8} {'Avg Runtime(s)':<15} {'Runtime Std(s)':<15}")
        print("-" * 80)
        
        for op_name, stats in self.destroy_stats.items():
            score = self.get_destroy_operator_score(op_name)
            runtime_stats = self.get_runtime_statistics(op_name, 'destroy')
            
            print(f"{op_name:<25} {stats['uses']:<6} {score:<8.3f} "
                  f"{runtime_stats['avg_runtime']:<15.4f} {runtime_stats['std_runtime']:<15.4f}")
        
        # Repair operators  
        print("\n🔧 REPAIR OPERATORS:")
        print(f"{'Operator':<25} {'Uses':<6} {'Success Rate':<12} {'Avg Improve':<12} "
              f"{'Score':<8} {'Avg Runtime(s)':<15} {'Runtime Std(s)':<15}")
        print("-" * 110)
        
        for op_name, stats in self.repair_stats.items():
            score = self.get_repair_operator_score(op_name)
            runtime_stats = self.get_runtime_statistics(op_name, 'repair')
            
            print(f"{op_name:<25} {stats['uses']:<6} {stats['repair_efficiency']:<12.2%} "
                  f"{stats['avg_improvement']:<12.1f} {score:<8.3f} "
                  f"{runtime_stats['avg_runtime']:<15.4f} {runtime_stats['std_runtime']:<15.4f}")
        
        # Runtime performance ranking
        print("\n⚡ RUNTIME PERFORMANCE RANKING:")
        
        # Destroy operators ranking
        destroy_runtimes = [(name, self.get_runtime_statistics(name, 'destroy')['avg_runtime']) 
                           for name in self.destroy_stats.keys() 
                           if self.destroy_stats[name]['uses'] > 0]
        destroy_runtimes.sort(key=lambda x: x[1])
        
        print("\n📊 Destroy Operators (Fastest → Slowest):")
        for i, (name, runtime) in enumerate(destroy_runtimes, 1):
            speedup = destroy_runtimes[-1][1] / runtime if runtime > 0 else float('inf')
            print(f"  {i}. {name:<25} {runtime:<10.4f}s (×{speedup:.1f} faster than slowest)")
        
        # Repair operators ranking
        repair_runtimes = [(name, self.get_runtime_statistics(name, 'repair')['avg_runtime']) 
                          for name in self.repair_stats.keys() 
                          if self.repair_stats[name]['uses'] > 0]
        repair_runtimes.sort(key=lambda x: x[1])
        
        print("\n📊 Repair Operators (Fastest → Slowest):")
        for i, (name, runtime) in enumerate(repair_runtimes, 1):
            speedup = repair_runtimes[-1][1] / runtime if runtime > 0 else float('inf')
            print(f"  {i}. {name:<25} {runtime:<10.4f}s (×{speedup:.1f} faster than slowest)")
        
        # Best combinations
        print("\n🤝 BEST OPERATOR COMBINATIONS:")
        if self.combo_stats:
            sorted_combos = sorted(self.combo_stats.items(), 
                                 key=lambda x: x[1]['avg_improvement'], reverse=True)
            for combo, stats in sorted_combos[:3]:  # Top 3
                print(f"  {combo}: Avg Improvement={stats['avg_improvement']:.1f}, "
                      f"Success Rate={stats['success_rate']:.2%}, Uses={stats['uses']}")
        
        # Performance insights
        print("\n💡 PERFORMANCE INSIGHTS:")
        if repair_runtimes:
            fastest_repair = repair_runtimes[0][0]
            slowest_repair = repair_runtimes[-1][0]
            
            fastest_stats = self.repair_stats[fastest_repair]
            slowest_stats = self.repair_stats[slowest_repair]
            
            print(f"  • Fastest repair: {fastest_repair} ({repair_runtimes[0][1]:.4f}s avg)")
            print(f"  • Slowest repair: {slowest_repair} ({repair_runtimes[-1][1]:.4f}s avg)")
            print(f"  • Speed difference: {repair_runtimes[-1][1]/repair_runtimes[0][1]:.1f}×")
            
            # Efficiency vs Speed trade-off
            fastest_score = self.get_repair_operator_score(fastest_repair)
            slowest_score = self.get_repair_operator_score(slowest_repair)
            print(f"  • Speed vs Quality: {fastest_repair} (score: {fastest_score:.3f}) vs "
                  f"{slowest_repair} (score: {slowest_score:.3f})")

class ChargerType(Enum):
    """Types of charging infrastructure"""
    TYPE_A = "A"
    TYPE_B = "B" 
    TYPE_C = "C"

@dataclass
class Depot:
    """Represents a bus depot with charging infrastructure"""
    id: int
    location: Tuple[float, float]
    chargers: Dict[ChargerType, int]  # Number of each charger type
    charging_power: Dict[ChargerType, float]  # Charging power (kW) for each type
    
@dataclass
class Stop:
    """Represents a bus stop"""
    id: int
    location: Tuple[float, float]
    route_id: int
    position_on_route: int

@dataclass
class Route:
    """Represents a bus route"""
    id: int
    stops: List[Stop]
    travel_times: List[float]  # Travel time between consecutive stops
    energy_consumption: List[float]  # Energy consumption between consecutive stops

@dataclass
class Bus:
    """Represents an electric bus"""
    id: int
    home_depot: int
    capacity: int  # Maximum passenger capacity
    max_soc: float  # Maximum State of Charge (kWh)
    min_soc: float  # Minimum safe State of Charge (kWh)
    current_soc: float = None  # Current SoC, initialized to max_soc

    def __post_init__(self):
        if self.current_soc is None:
            #* 初始化时，随机选择一个在50%到80%之间的SoC
            self.current_soc = self.max_soc * random.uniform(0.3, 0.7)

@dataclass
class Passenger:
    """Represents a passenger"""
    id: int
    origin_stop: int
    destination_stop: int
    arrival_time: float
    route_id: int
    boarding_time: Optional[float] = None  # 是 Python 类型注解中的一种，用于表示某个变量可以是指定类型，也可以是 None。这样可以方便地表示“该乘客还未上车”或“上车时间未知”的情况。
    waiting_time: float = 0.0

@dataclass
class Trip:
    """Represents a single bus trip on a route"""
    route_id: int
    departure_time: float
    arrival_times: List[float]  # Arrival time at each stop
    departure_times: List[float]  # Departure time at each stop
    passenger_load: List[int]  # Number of passengers on bus after each stop
    passengers_boarded: List[List[Passenger]]  # Passengers boarded at each stop
    passengers_alighted: List[List[Passenger]]  # Passengers alighted at each stop
    energy_consumed: float = 0.0

@dataclass 
class ChargingEvent:
    """Represents a charging event at a depot"""
    depot_id: int
    charger_type: ChargerType
    start_time: float
    end_time: float
    energy_added: float

@dataclass
class BusTour:
    """Represents the complete schedule for a single bus"""
    bus_id: int
    trips: List[Trip] = field(default_factory=list)
    charging_events: List[ChargingEvent] = field(default_factory=list)
    final_return_time: float = 0.0
    
    def is_feasible(self, bus: Bus, planning_horizon: float, return_buffer: float) -> bool:
        """Check if this tour is feasible for the given bus"""
        # Check if bus returns within time limit
        if self.final_return_time > planning_horizon + return_buffer:
            return False
        return True

class PassengerQueue:
    """Manages passenger queues at bus stops with FIFO ordering"""
    
    def __init__(self):
        self.queues: Dict[int, deque] = defaultdict(deque)  # stop_id -> passenger queue
    
    def add_passenger(self, stop_id: int, passenger: Passenger):
        """Add passenger to stop queue"""
        self.queues[stop_id].append(passenger)
    
    def board_passengers(self, stop_id: int, bus_capacity: int, current_load: int) -> List[Passenger]:
        """Board passengers from queue respecting capacity and FIFO order"""
        available_capacity = bus_capacity - current_load
        boarded = []
        
        queue = self.queues[stop_id]
        while queue and len(boarded) < available_capacity:
            passenger = queue.popleft()
            boarded.append(passenger)
            
        return boarded
    
    def get_queue_length(self, stop_id: int) -> int:
        """Get current queue length at stop"""
        return len(self.queues[stop_id])

class Solution:
    """Represents a complete solution to the evacuation problem"""
    
    def __init__(self, buses: List[Bus]):
        self.bus_tours: List[BusTour] = [BusTour(bus.id) for bus in buses]
        self.passenger_queues = PassengerQueue()
        self.total_passengers_evacuated = 0
        self.objective_value = 0.0
        self.penalty_value = 0.0
        self.hard_penalty = 0.0
        self.time_violations = 0.0
        self.soc_violations = 0.0
        self.vehicle_usage_penalty = 0.0
        self.is_feasible = True
        
    def copy(self) -> 'Solution':
        """Create a deep copy of the solution"""
        new_solution = Solution([])
        new_solution.bus_tours = copy.deepcopy(self.bus_tours)
        new_solution.passenger_queues = copy.deepcopy(self.passenger_queues)
        new_solution.total_passengers_evacuated = self.total_passengers_evacuated
        new_solution.objective_value = self.objective_value
        new_solution.penalty_value = self.penalty_value
        new_solution.is_feasible = self.is_feasible
        
        # Copy new attributes
        new_solution.hard_penalty = getattr(self, 'hard_penalty', 0.0)
        new_solution.time_violations = getattr(self, 'time_violations', 0.0)
        new_solution.soc_violations = getattr(self, 'soc_violations', 0.0)
        new_solution.vehicle_usage_penalty = getattr(self, 'vehicle_usage_penalty', 0.0)
        
        return new_solution

class ChargerResourceManager:
    """Manages charging infrastructure resource allocation"""
    
    def __init__(self, depots: List[Depot]):
        self.depots = {depot.id: depot for depot in depots}
        # Track charger usage over time: depot_id -> charger_type -> [(start_time, end_time), ...]
        self.charger_schedule: Dict[int, Dict[ChargerType, List[Tuple[float, float]]]] = defaultdict(
            lambda: defaultdict(list)
        )
    
    def is_charger_available(self, depot_id: int, charger_type: ChargerType, 
                           start_time: float, end_time: float) -> bool:
        """Check if charger is available during specified time window"""
        depot = self.depots[depot_id]
        max_chargers = depot.chargers[charger_type]
        
        # Count overlapping charging sessions
        overlapping_sessions = 0
        for session_start, session_end in self.charger_schedule[depot_id][charger_type]:
            if not (end_time <= session_start or start_time >= session_end):
                overlapping_sessions += 1
                
        return overlapping_sessions < max_chargers
    
    def reserve_charger(self, depot_id: int, charger_type: ChargerType,
                       start_time: float, end_time: float) -> bool:
        """Reserve a charger for the specified time window"""
        if self.is_charger_available(depot_id, charger_type, start_time, end_time):
            self.charger_schedule[depot_id][charger_type].append((start_time, end_time))
            return True
        return False
    
    def release_charger(self, depot_id: int, charger_type: ChargerType,
                       start_time: float, end_time: float):
        """Release a charger reservation"""
        try:
            self.charger_schedule[depot_id][charger_type].remove((start_time, end_time))
        except ValueError:
            pass  # Reservation not found

class ElectricBusEvacuationALNS:
    """Main ALNS algorithm for Electric Bus Evacuation Scheduling"""
    
    def __init__(self, buses: List[Bus], routes: List[Route], depots: List[Depot],
                 passengers: List[Passenger], planning_horizon: float, return_buffer: float,
                 logger: SolutionLogger = None):
        self.buses = buses
        self.routes = {route.id: route for route in routes}
        self.depots = depots
        self.passengers = passengers
        self.planning_horizon = planning_horizon
        self.return_buffer = return_buffer
        self.boarding_time_per_person = 0.05  # Boarding/alighting time for each passenger (minutes)
        self.base_time = 0.1  # Base dwell time at each stop (minutes) 
        self.soc_min_proportion = 0.2  # Minimum state of charge proportion for bus operation
        self.bus_travel_speed = 20 # km/h
        
        # Initialize logger
        self.logger = logger
        
        # Initialize charger resource manager for handling charging conflicts
        self.charger_manager = ChargerResourceManager(depots)

        # ALNS parameters
        self.max_iterations = 100
        self.temperature_start = 100.0
        self.temperature_end = 1.0
        self.alpha = 0.9995  # Temperature decay factor
        
        # Penalty weights
        self.vehicle_usage_penalty_weight = 50  # Penalty per bus used (encourage fewer buses)
        
        # Simplified operator weights - only keep effective ones
        self.destroy_weights = {
            'random_trip_removal': 1.0,
            'route_based_removal': 1.0,
            'time_based_removal': 1.0,
            'time_shift_removal': 1.0,
        }
        self.repair_weights = {
            'greedy_insertion': 1.0,
            'time_aware_greedy_insertion': 1.0, 
            'time_shift_insertion': 1.0,
            'regret_insertion': 1.0,
        }
        
        # Operator performance tracking
        self.operator_evaluator = OperatorEvaluator()
        
        # Initialize passenger queues based on arrival times
        # Sort passengers by origin_stop first, then by arrival_time to ensure FIFO within each stop
        passengers_sorted = sorted(passengers, key=lambda p: (p.origin_stop, p.arrival_time))
        
        self.initial_passenger_queues = PassengerQueue()
        for passenger in passengers_sorted:
            self.initial_passenger_queues.add_passenger(passenger.origin_stop, passenger)
    
    def generate_initial_solution(self) -> Solution:
        """Generate an initial feasible solution using greedy construction"""
        # 目的：为ALNS算法提供一个初始可行解，保证后续扰动和修复操作有良好起点。
        # 策略：每辆公交优先选择能搭载最多乘客的行程，自动考虑电量和充电需求，直到时间或资源耗尽。
        solution = Solution(self.buses)
        solution.passenger_queues = copy.deepcopy(self.initial_passenger_queues)
        
        # Reset charger resource manager for fresh solution generation
        self._reset_charger_resources()
        
        # Simple greedy approach: assign trips to buses in order
        current_time = 0.0
        trip_interval = 20.0  # minutes between trips
        
        for bus in self.buses:
            bus_tour = solution.bus_tours[bus.id]
            current_bus_time = current_time
            current_soc = bus.current_soc
            last_route_id = None  # 记录上一个行程的路线ID
            
            # Keep adding trips while feasible
            while current_bus_time + trip_interval <= self.planning_horizon:
                # Try to create a trip on each route
                best_trip = None
                best_passengers = 0
                
                for route_id in self.routes:
                    # 对每条线路尝试生成trip候选，选择能搭载最多乘客的trip
                    # 使用临时队列副本避免候选之间互相影响
                    temp_queues = copy.deepcopy(solution.passenger_queues)
                    trip_candidate = self._create_trip_candidate_with_context(
                        route_id, current_bus_time, bus, current_soc, temp_queues, last_route_id
                    )

                    if trip_candidate and self._is_trip_feasible_enhanced(trip_candidate, bus, current_soc):
                        # Count potential passengers
                        potential_passengers = sum(len(p) for p in trip_candidate.passengers_boarded)
                        if potential_passengers > best_passengers:
                            best_trip = trip_candidate
                            best_passengers = potential_passengers
                
                if best_trip:
                    # 重新创建最佳行程，使用实际的乘客队列
                    final_trip = self._create_trip_candidate_with_context(
                        best_trip.route_id, current_bus_time, bus, current_soc, solution.passenger_queues, last_route_id
                    )

                    if final_trip:
                        bus_tour.trips.append(final_trip)
                        best_trip = final_trip  # 使用实际创建的行程
                    else:
                        break  # 如果无法创建实际行程，停止

                    # 更新时间和电量 - 考虑是否需要返回车场
                    needs_charging = (current_soc - best_trip.energy_consumed) < bus.max_soc * self.soc_min_proportion
                
                    if needs_charging:
                        # 需要充电：返回车场
                        deadhead_time = self._get_deadhead_time_to_depot(best_trip.route_id, bus.home_depot)
                        current_bus_time = best_trip.departure_times[-1] + deadhead_time
                        last_route_id = None  # 重置，下次从车场出发
                    else:
                        # 不需要充电：直接前往下一个行程起点
                        current_bus_time = best_trip.departure_times[-1]
                        last_route_id = best_trip.route_id  # 记录当前路线

                    current_soc -= best_trip.energy_consumed
                    
                    # Add charging if needed
                    if needs_charging:  # Charge when below 20%
                        charging_event = self._plan_charging(bus, current_soc, current_bus_time)
                        if charging_event:
                            bus_tour.charging_events.append(charging_event)
                            current_soc += charging_event.energy_added
                            current_bus_time = charging_event.end_time
                else:
                    break
            
            # Set final return time
            if bus_tour.trips:
                final_deadhead_time = self._get_deadhead_time_to_depot(
                    bus_tour.trips[-1].route_id, bus.home_depot
                )
                bus_tour.final_return_time = current_bus_time + final_deadhead_time
            else:
                bus_tour.final_return_time = current_bus_time
        
        # Evaluate solution
        self._evaluate_solution(solution)
        return solution
    
    def _create_trip_candidate_with_time_optimization(self, route_id: int, earliest_time: float,
                                                latest_time: float, bus: Bus, current_soc: float,
                                                passenger_queues: PassengerQueue,
                                                last_route_id: Optional[int] = None) -> Optional[Trip]:
        """Create trip candidate with optimal departure time within time window"""
        
        # Sample multiple departure times within feasible window
        # 在时间窗内采样多个出发时间点
        time_samples = np.linspace(earliest_time, latest_time, min(10, int(latest_time - earliest_time) + 1))
        
        best_trip = None
        best_passenger_count = 0
        
        for departure_time in time_samples:
            # Create trip candidate for this departure time
            trip_candidate = self._create_trip_candidate_with_context(
                route_id, departure_time, bus, current_soc, 
                copy.deepcopy(passenger_queues), last_route_id
            )
            
            if trip_candidate and self._is_trip_feasible_enhanced(trip_candidate, bus, current_soc):
                passenger_count = sum(len(p) for p in trip_candidate.passengers_boarded)
                # 选择能接载最多乘客的时间点
                if passenger_count > best_passenger_count:
                    best_trip = trip_candidate
                    best_passenger_count = passenger_count
        
        return best_trip

    def _calculate_feasible_time_window(self, bus_tour: BusTour, route_id: int, 
                                    bus: Bus, current_soc: float) -> Tuple[float, float]:
        """Calculate feasible departure time window for new trip"""
        
        # Earliest time: after previous activity
        if bus_tour.trips:
            last_trip = bus_tour.trips[-1]
            earliest_time = last_trip.departure_times[-1]
            
            # Add deadhead time if needed
            if current_soc < bus.max_soc * self.soc_min_proportion:
                # Need charging
                charging_time = self._estimate_charging_time(bus, current_soc)
                earliest_time += charging_time
            else:
                # Direct route-to-route travel
                deadhead_time = self._get_route_to_route_time(last_trip.route_id, route_id)
                earliest_time += deadhead_time
        else:
            # First trip: from depot
            depot_time = self._get_depot_to_start_time(bus.home_depot, route_id)
            earliest_time = depot_time
        
        # Latest time: must return before planning horizon
        estimated_trip_duration = self._estimate_trip_duration(route_id)
        return_time = self._get_deadhead_time_to_depot(route_id, bus.home_depot)
        latest_time = self.planning_horizon - estimated_trip_duration - return_time - self.return_buffer
        
        return max(0, earliest_time), max(earliest_time, latest_time)
    
    def _create_trip_candidate_with_context(self, route_id: int, departure_time: float, 
                                      bus: Bus, current_soc: float, passenger_queues: PassengerQueue,
                                      last_route_id: Optional[int] = None) -> Optional[Trip]:
        """Create a candidate trip considering previous trip context"""
        route = self.routes[route_id]
        
        # 计算到达首发站的时间和能耗
        if last_route_id is None:
            # 从车场出发
            travel_to_start_time = self._get_depot_to_start_time(bus.home_depot, route_id)
            travel_to_start_energy = travel_to_start_time * 1.75 / 3
        else:
            # 从上一个路线的终点出发
            travel_to_start_time = self._get_route_to_route_time(last_route_id, route_id)
            travel_to_start_energy = travel_to_start_time * 1.75 / 3
        
        # 调整实际出发时间
        actual_departure_time = departure_time + travel_to_start_time
        
        trip = Trip(
            route_id=route_id,
            departure_time=actual_departure_time,
            arrival_times=[],
            departure_times=[],
            passenger_load=[],
            passengers_boarded=[[] for _ in route.stops],
            passengers_alighted=[[] for _ in route.stops]
        )
        
        current_time = actual_departure_time
        current_load = 0
        total_energy = travel_to_start_energy  # 包含到达首发站的能耗
        
        # 原有的站点处理逻辑保持不变
        for i, stop in enumerate(route.stops):
            if i > 0:
                travel_time = route.travel_times[i-1]
                energy_consumption = route.energy_consumption[i-1]
                current_time += travel_time
                total_energy += energy_consumption
            
            trip.arrival_times.append(current_time)
            
            # Passenger alighting
            alighting_passengers = [p for p in self._get_passengers_on_bus(trip, i) 
                                if p.destination_stop == stop.id]
            trip.passengers_alighted[i] = alighting_passengers
            current_load -= len(alighting_passengers)
            
            # Passenger boarding 
            boarded_passengers = passenger_queues.board_passengers(
                stop.id, bus.capacity, current_load
            )
            trip.passengers_boarded[i] = boarded_passengers
            current_load += len(boarded_passengers)
            
            # Calculate dwell time
            dwell_time = self._calculate_dwell_time(
                len(boarded_passengers), len(alighting_passengers)
            )
            current_time += dwell_time
            
            trip.departure_times.append(current_time)
            trip.passenger_load.append(current_load)
        
        trip.energy_consumed = total_energy
        return trip

    def _get_route_to_route_time(self, from_route_id: int, to_route_id: int) -> float:
        """计算从一个路线终点到另一个路线起点的时间"""
        try:
            from_route = self.routes[from_route_id]
            to_route = self.routes[to_route_id]
            
            if not from_route.stops or not to_route.stops:
                return 10.0
            
            # 从路线终点到路线起点
            from_location = from_route.stops[-1].location
            to_location = to_route.stops[0].location
            
            distance = self._calculate_euclidean_distance(from_location, to_location)
            travel_time = distance / 4
            
            return max(5.0, travel_time)
            
        except Exception as e:
            print(f"Error calculating route-to-route time from {from_route_id} to {to_route_id}: {e}")
            return 10.0
    
    def _get_depot_to_start_time(self, depot_id: int, route_id: int) -> float:
        """计算从车场到路线首发站的时间"""
        try:
            # 找到车场
            depot = next(d for d in self.depots if d.id == depot_id)
            depot_location = depot.location
            
            # 找到路线首发站
            route = self.routes[route_id]
            if not route.stops:
                return 10.0  # 默认时间
            
            first_stop = route.stops[0]
            first_stop_location = first_stop.location
            
            # 计算距离和时间
            distance = self._calculate_euclidean_distance(depot_location, first_stop_location)
            travel_time = distance / 4  #! 使用相同的速度计算
            
            return max(5.0, travel_time)  # 最少5分钟
            
        except Exception as e:
            print(f"Error calculating depot to start time for depot {depot_id} to route {route_id}: {e}")
            return 10.0
    
    def _is_trip_feasible_enhanced(self, trip: Trip, bus: Bus, current_soc: float) -> bool:
        """Check if a trip is feasible given current bus state"""
        # Check SoC constraint
        if current_soc - trip.energy_consumed < bus.min_soc:
            return False
        
        # Check capacity constraint
        max_load = max(trip.passenger_load) if trip.passenger_load else 0
        if max_load > bus.capacity:
            return False
        
        return True
    
    def _is_trip_feasible_enhanced(self, trip: Trip, bus: Bus, current_soc: float) -> bool:
        """Enhanced feasibility check including all constraints"""
        
        # 1. Basic SoC constraint
        if current_soc - trip.energy_consumed < bus.min_soc:
            return False
        
        # 2. Capacity constraint
        max_load = max(trip.passenger_load) if trip.passenger_load else 0
        if max_load > bus.capacity:
            return False
        
        # 3. Time constraint: check if bus can return to depot after this trip
        trip_end_time = trip.departure_times[-1] if trip.departure_times else trip.departure_time
        deadhead_time = self._get_deadhead_time_to_depot(trip.route_id, bus.home_depot)
        total_return_time = trip_end_time + deadhead_time
        
        if total_return_time > self.planning_horizon + self.return_buffer:
            return False
        
        # 4. Charging feasibility: if SoC is low, check if charging is possible
        remaining_soc = current_soc - trip.energy_consumed
        if remaining_soc < bus.max_soc * self.soc_min_proportion:
            # Need charging - check if charger is available
            charging_start_time = trip_end_time + deadhead_time
            
            # Try to find available charger

            depot = next(d for d in self.depots if d.id == bus.home_depot)
        
            charging_possible = False
            
            for charger_type in [ChargerType.TYPE_C, ChargerType.TYPE_B, ChargerType.TYPE_A]:
                if charger_type in depot.chargers and depot.chargers[charger_type] > 0:
                    # Calculate realistic charging energy needed
                    current_soc_after_trip = remaining_soc
                    target_soc = current_soc_after_trip + (bus.max_soc - current_soc_after_trip) * 0.3  # 50% charging
                    energy_needed = max(0, target_soc - current_soc_after_trip)
                    
                    if energy_needed > 0:
                        charging_power = depot.charging_power[charger_type]
                        charging_time = energy_needed / charging_power * 60  # Convert to minutes
                        charging_end_time = charging_start_time + charging_time
                        
                        # Check if charging completes within time limit
                        if charging_end_time > self.planning_horizon + self.return_buffer:
                            continue  # This charger type takes too long
                        
                        # Check charger availability
                        if self.charger_manager.is_charger_available(
                            depot.id, charger_type, charging_start_time, charging_end_time
                        ):
                            charging_possible = True
                            break
                    else:
                        # No charging needed after all
                        charging_possible = True
                        break
            
            if not charging_possible:
                return False
        
        return True
    
    def _get_passengers_on_bus(self, trip: Trip, stop_index: int) -> List[Passenger]:
        """Get list of passengers currently on bus at given stop"""
        passengers_on_bus = []
        
        # Add passengers boarded at previous stops who haven't alighted yet
        for i in range(stop_index):
            for passenger in trip.passengers_boarded[i]: # 遍历所有已上车的乘客
                # Check if passenger hasn't alighted yet
                alighted = False
                for j in range(i+1, stop_index+1):
                    if passenger in trip.passengers_alighted[j]: # 判断乘客是否已经下车
                        alighted = True
                        break
                if not alighted: # 收集还在车上的乘客
                    passengers_on_bus.append(passenger)
        
        return passengers_on_bus
    
    def _calculate_dwell_time(self, boarding_count: int, alighting_count: int) -> float:
        """Calculate bus dwell time at stop"""
        # base_time = 0.5  # Base dwell time in minutes
        return self.base_time + max(boarding_count, alighting_count) * self.boarding_time_per_person
    
    def _get_deadhead_time_to_depot(self, route_id: int, depot_id: int) -> float:
        """Get deadhead time from route end to depot based on actual geographic distance"""
        try:
            # Get the route and its last stop
            if route_id not in self.routes:
                print(f"Warning: Route {route_id} not found, using default deadhead time")
                return 10.0

            route = self.routes[route_id]
            if not route.stops:
                print(f"Warning: Route {route_id} has no stops, using default deadhead time")
                return 10.0

            # Get the last stop's location
            last_stop = route.stops[-1]
            last_stop_location = last_stop.location

            # Find the depot
            depot = None
            for d in self.depots:
                if d.id == depot_id:
                    depot = d
                    break

            if depot is None:
                print(f"Warning: Depot {depot_id} not found, using default deadhead time")
                return 10.0

            depot_location = depot.location

            # Calculate Euclidean distance
            distance = self._calculate_euclidean_distance(last_stop_location, depot_location)

            # Convert distance to time (assuming average speed of 4 distance units per minute)
            #! 后续将distance 调整为公里数之后该行代码改为
            #! deadhead_time = distance / self.bus_travel_speed * 60
            deadhead_time = distance / 4

            # Ensure minimum deadhead time of 1 minute
            return max(5.0, deadhead_time)

        except Exception as e:
            print(f"Error calculating deadhead time for route {route_id} to depot {depot_id}: {e}")
            return 10.0  # Fallback to default

    def _calculate_euclidean_distance(self, point1: Tuple[float, float], point2: Tuple[float, float]) -> float:
        """Calculate Euclidean distance between two points"""
        import math
        x1, y1 = point1
        x2, y2 = point2
        return math.sqrt((x2 - x1)**2 + (y2 - y1)**2)
    
    def _plan_charging(self, bus: Bus, current_soc: float, start_time: float) -> Optional[ChargingEvent]:
        """Plan charging event for bus with proper resource management"""
        #! 默认车辆一定会回到其所属车场进行充电
        depot = next(d for d in self.depots if d.id == bus.home_depot)

        # Calculate adaptive charging ratio based on remaining time
        remaining_time = self.planning_horizon - start_time
        time_ratio = remaining_time / self.planning_horizon

        #! Charging ratio: more remaining time = higher ratio (0.2-1.0)
        charging_ratio = 0.2 + 0.8 * time_ratio

        # Calculate target SoC and energy needed
        target_soc = current_soc + (bus.max_soc - current_soc) * charging_ratio
        energy_needed = target_soc - current_soc
        
        # Try different charger types (prefer faster charging)
        for charger_type in [ChargerType.TYPE_C, ChargerType.TYPE_B, ChargerType.TYPE_A]:
            if charger_type in depot.chargers and depot.chargers[charger_type] > 0:
                charging_power = depot.charging_power[charger_type]
                charging_time = energy_needed / charging_power * 60  # Convert to minutes
                
                end_time = start_time + charging_time
                
                # 🔧 使用ChargerResourceManager检查充电桩可用性
                if self.charger_manager.is_charger_available(depot.id, charger_type, start_time, end_time):
                    # 预订充电桩
                    if self.charger_manager.reserve_charger(depot.id, charger_type, start_time, end_time):
                        return ChargingEvent(
                            depot_id=depot.id,
                            charger_type=charger_type,
                            start_time=start_time,
                            end_time=end_time,
                            energy_added=energy_needed
                        )
                    else:
                        # 预订失败，尝试下一种充电桩类型
                        continue
                else:
                    # 充电桩不可用，尝试下一种类型
                    continue
        
        # 所有类型的充电桩都不可用
        return None
    
    def _reset_charger_resources(self):
        """Reset charger resource manager to clean state"""
        self.charger_manager = ChargerResourceManager(self.depots)
    
    def _update_charger_resources_from_solution(self, solution: Solution):
        """Update charger resource manager based on current solution"""
        # Reset first
        self._reset_charger_resources()
        
        # Re-reserve all charging events in the solution
        for bus_tour in solution.bus_tours:
            for charging_event in bus_tour.charging_events:
                # Reserve the charger for this event
                success = self.charger_manager.reserve_charger(
                    charging_event.depot_id,
                    charging_event.charger_type,
                    charging_event.start_time,
                    charging_event.end_time
                )
                if not success:
                    print(f"⚠️  Warning: Failed to reserve charger for bus {bus_tour.bus_id} "
                          f"at depot {charging_event.depot_id}")
    
    def _check_charger_conflicts(self, solution: Solution) -> int:
        """Check for charger resource conflicts in the solution"""
        conflicts = 0
        
        # Create temporary resource manager to check conflicts
        temp_charger_manager = ChargerResourceManager(self.depots)
        
        # Try to reserve all charging events and count conflicts
        for bus_tour in solution.bus_tours:
            for charging_event in bus_tour.charging_events:
                if not temp_charger_manager.is_charger_available(
                    charging_event.depot_id,
                    charging_event.charger_type,
                    charging_event.start_time,
                    charging_event.end_time
                ):
                    conflicts += 1
                else:
                    # Reserve if available
                    temp_charger_manager.reserve_charger(
                        charging_event.depot_id,
                        charging_event.charger_type,
                        charging_event.start_time,
                        charging_event.end_time
                    )
        
        return conflicts

    def _evaluate_solution(self, solution: Solution):
        """Evaluate solution quality and feasibility"""
        total_passengers = 0
        hard_penalty = 0.0  # Only for feasibility constraints
        
        # Count number of buses used (buses with at least one trip)
        buses_used = 0
        
        # Check charger resource conflicts
        charger_violations = self._check_charger_conflicts(solution)
        hard_penalty += charger_violations * 1000  # Heavy penalty for charger conflicts
        
        # Detailed constraint violation tracking
        time_violations = 0
        soc_violations = 0

        for bus_tour in solution.bus_tours:
            # Count passengers evacuated
            for trip in bus_tour.trips:
                for passengers_at_stop in trip.passengers_boarded:
                    total_passengers += len(passengers_at_stop)
            
            # Count buses that have at least one trip
            if len(bus_tour.trips) > 0:
                buses_used += 1
            
            # Check feasibility constraints
            bus = self.buses[bus_tour.bus_id]
            
            # Time constraint penalty (hard constraint)
            if bus_tour.final_return_time > self.planning_horizon + self.return_buffer:
                violation = bus_tour.final_return_time - self.planning_horizon - self.return_buffer
                hard_penalty += 1000 * violation
                time_violations += 1000 * violation
            
            # SoC constraint penalty (hard constraint)
            current_soc = bus.max_soc
            for trip in bus_tour.trips:
                current_soc -= trip.energy_consumed
                if current_soc < bus.min_soc:
                    violation = bus.min_soc - current_soc
                    hard_penalty += 1000 * violation
                    soc_violations += 1000 * violation
                
                # Add charging energy
                for charging in bus_tour.charging_events:
                    if charging.start_time >= trip.departure_times[-1]:
                        current_soc += charging.energy_added
                        break
        
        # Vehicle usage penalty: encourage using fewer buses (soft constraint)
        vehicle_usage_penalty = buses_used * self.vehicle_usage_penalty_weight
        
        # Total penalty = hard constraints + soft constraints
        total_penalty = hard_penalty + vehicle_usage_penalty
        solution.total_passengers_evacuated = total_passengers
        solution.objective_value = total_passengers
        solution.penalty_value = total_penalty
        solution.is_feasible = (hard_penalty == 0)  # Only based on hard constraints

        solution.hard_penalty = hard_penalty  # Add this attribute
        solution.vehicle_usage_penalty = vehicle_usage_penalty  # Add this attribute
        # Store violation details for debugging
        solution.time_violations = time_violations
        solution.soc_violations = soc_violations
    
    # ALNS Destroy Operators
    def _random_trip_removal(self, solution: Solution, num_remove: int) -> Solution:
        """Randomly remove trips from solution"""
        new_solution = solution.copy()
        
        # Collect all trips
        all_trips = []
        for bus_tour in new_solution.bus_tours:
            for i, trip in enumerate(bus_tour.trips):
                all_trips.append((bus_tour.bus_id, i, trip))
        
        # Randomly select trips to remove
        trips_to_remove = random.sample(all_trips, min(num_remove, len(all_trips)))
        
        # Remove selected trips
        for bus_id, trip_index, trip in sorted(trips_to_remove, 
                                              key=lambda x: x[1], reverse=True):
            new_solution.bus_tours[bus_id].trips.pop(trip_index)
        
        return new_solution
    
    def _route_based_removal(self, solution: Solution, num_remove: int) -> Solution:
        """Remove trips from same route"""
        new_solution = solution.copy()
        
        # Select random route
        route_ids = list(self.routes.keys())
        target_route = random.choice(route_ids)
        
        # Remove trips from this route
        removed_count = 0
        for bus_tour in new_solution.bus_tours:
            trips_to_remove = []
            for i, trip in enumerate(bus_tour.trips):
                if trip.route_id == target_route and removed_count < num_remove:
                    trips_to_remove.append(i)
                    removed_count += 1
            
            # Remove trips in reverse order to maintain indices
            for i in reversed(trips_to_remove):
                bus_tour.trips.pop(i)
        
        return new_solution
    
    def _time_based_removal(self, solution: Solution, num_remove: int) -> Solution:
        """Remove trips in specific time window"""
        new_solution = solution.copy()
        
        # Select random time window
        window_start = random.uniform(0, self.planning_horizon - 30)
        window_end = window_start + 30  # 30-minute window
        
        # Remove trips in this time window
        removed_count = 0
        for bus_tour in new_solution.bus_tours:
            trips_to_remove = []
            for i, trip in enumerate(bus_tour.trips):
                if (window_start <= trip.departure_time <= window_end and 
                    removed_count < num_remove):
                    trips_to_remove.append(i)
                    removed_count += 1
            
            # Remove trips in reverse order
            for i in reversed(trips_to_remove):
                bus_tour.trips.pop(i)
        
        return new_solution
    
    def _time_shift_removal(self, solution: Solution, num_remove: int) -> Solution:
        """
        Remove trips and allow time-shifted reinsertion
        ✅ 智能选择: 不是随机移除，而是选择时间调整空间最大的trips
        ✅ 保护约束: 考虑前后trip的时间依赖关系
        ✅ 为优化铺路: 为repair阶段的时间优化创造更多可能性
        """
        new_solution = solution.copy()
        
        # Collect all trips with their time flexibility
        flexible_trips = []
        # 分析每个trip的时间灵活性
        for bus_id, bus_tour in enumerate(new_solution.bus_tours):
            for trip_idx, trip in enumerate(bus_tour.trips):
                # Calculate time flexibility for this trip
                # 计算时间偏移范围
                min_shift = -30.0  # Can shift 30 minutes earlier
                max_shift = 60.0   # Can shift 60 minutes later
                
                # Check constraints
                # 检查前后trip的约束
                if trip_idx > 0: # 不能早于前一个trip结束时间
                    prev_trip = bus_tour.trips[trip_idx - 1]
                    min_shift = max(min_shift, prev_trip.departure_times[-1] - trip.departure_time + 10)
                
                if trip_idx < len(bus_tour.trips) - 1: # 不能晚于后一个trip开始时间
                    next_trip = bus_tour.trips[trip_idx + 1]
                    max_shift = min(max_shift, next_trip.departure_time - trip.departure_times[-1] - 10)
                
                if max_shift > min_shift:
                    flexible_trips.append({
                        'bus_id': bus_id,
                        'trip_idx': trip_idx,
                        'trip': trip,
                        # 计算灵活性得分
                        'flexibility': max_shift - min_shift
                    })
        
        # Remove most flexible trips
        #* 优先移除最灵活的trips
        flexible_trips.sort(key=lambda x: x['flexibility'], reverse=True)
        trips_to_remove = flexible_trips[:min(num_remove, len(flexible_trips))]
        
        # Remove selected trips (in reverse order to maintain indices)
        for removal in sorted(trips_to_remove, key=lambda x: x['trip_idx'], reverse=True):
            new_solution.bus_tours[removal['bus_id']].trips.pop(removal['trip_idx'])
        
        return new_solution
    
    # ALNS Repair Operators
    def _greedy_insertion(self, solution: Solution) -> Solution:
        """Insert unserved passengers using greedy approach"""
        new_solution = solution.copy()

        # 保持现有的乘客队列状态，不要重新创建
        # new_solution.passenger_queues 已经在 copy() 中正确复制了
        
        # Remove passengers already served in current solution
        self._update_passenger_queues_after_solution(new_solution)
        
        # Update charger resource manager based on existing charging events
        self._update_charger_resources_from_solution(new_solution)
        
        # Try to add new trips greedily
        for bus in self.buses:
            bus_tour = new_solution.bus_tours[bus.id]
            
            # Find next available time for this bus and last route context
            next_time = 0.0
            current_soc = bus.current_soc
            last_route_id = None  # Track last route for context
            
            if bus_tour.trips:
                last_trip = bus_tour.trips[-1]
                # Check if bus needs to return to depot for charging
                projected_soc = current_soc
                for trip in bus_tour.trips:
                    projected_soc -= trip.energy_consumed
                for charging in bus_tour.charging_events:
                    projected_soc += charging.energy_added
                
                # If SoC is low, bus must return to depot
                if projected_soc < bus.max_soc * self.soc_min_proportion:
                    next_time = last_trip.departure_times[-1] + self._get_deadhead_time_to_depot(
                        last_trip.route_id, bus.home_depot
                    )
                    last_route_id = None  # From depot
                else:
                    next_time = last_trip.departure_times[-1]
                    last_route_id = last_trip.route_id  # From last route
                
                # Calculate remaining SoC
                current_soc = bus.current_soc
                for trip in bus_tour.trips:
                    current_soc -= trip.energy_consumed
                for charging in bus_tour.charging_events:
                    current_soc += charging.energy_added
            
            # Try to add more trips
            while next_time <= self.planning_horizon:  #* Leave buffer for trip completion
                best_trip = None
                best_value = 0
                
                for route_id in self.routes:
                    trip_candidate = self._create_trip_candidate_with_context(
                        route_id, next_time, bus, current_soc, new_solution.passenger_queues, last_route_id
                    )
                    
                    if (trip_candidate and self._is_trip_feasible_enhanced(trip_candidate, bus, current_soc)):
                        #* Evaluate trip value (number of passengers)
                        value = sum(len(p) for p in trip_candidate.passengers_boarded)
                        # Greedy: 直接选择价值最高的候选
                        if value > best_value:
                            best_trip = trip_candidate
                            best_value = value
                
                if best_trip and best_value > 0:
                    bus_tour.trips.append(best_trip)
                    current_soc -= best_trip.energy_consumed
                    
                    # Determine next state based on charging needs
                    needs_charging = current_soc < bus.max_soc * self.soc_min_proportion
                    
                    if needs_charging:
                        # Must return to depot for charging
                        next_time = best_trip.departure_times[-1] + self._get_deadhead_time_to_depot(
                            best_trip.route_id, bus.home_depot
                        )
                        last_route_id = None  # Next trip from depot
                        
                        charging_event = self._plan_charging(bus, current_soc, next_time)
                        if charging_event:
                            bus_tour.charging_events.append(charging_event)
                            current_soc += charging_event.energy_added
                            next_time = charging_event.end_time
                        else:
                            break  # Can't charge, stop adding trips
                    else:
                        # Can continue from current route endpoint
                        next_time = best_trip.departure_times[-1]
                        last_route_id = best_trip.route_id  # Next trip from this route
                else:
                    break
            
            # Update final return time
            if bus_tour.trips:
                bus_tour.final_return_time = bus_tour.trips[-1].departure_times[-1] + \
                    self._get_deadhead_time_to_depot(bus_tour.trips[-1].route_id, bus.home_depot)
        
        self._evaluate_solution(new_solution)
        return new_solution
    
    # def _best_insertion(self, solution: Solution) -> Solution:
    #     """Insert using best position evaluation"""
        
    #     return self._greedy_insertion(solution)
    
    def _regret_insertion(self, solution: Solution) -> Solution:
        """Insert using regret-based evaluation - prioritizes trips that would lose most value if not selected"""
        new_solution = solution.copy()
        
        # Recreate passenger queues from remaining unserved passengers
        new_solution.passenger_queues = copy.deepcopy(self.initial_passenger_queues)
        self._update_passenger_queues_after_solution(new_solution)
        
        # Update charger resource manager based on existing charging events
        self._update_charger_resources_from_solution(new_solution)
        
        # Use greedy approach but with regret-based trip selection
        for bus in self.buses:
            bus_tour = new_solution.bus_tours[bus.id]
            
            # Find next available time for this bus and last route context
            next_time = 0.0
            current_soc = bus.current_soc
            last_route_id = None  # Track last route for context
            
            if bus_tour.trips:
                last_trip = bus_tour.trips[-1]
                # Check if bus needs to return to depot for charging
                projected_soc = current_soc
                for trip in bus_tour.trips:
                    projected_soc -= trip.energy_consumed
                for charging in bus_tour.charging_events:
                    projected_soc += charging.energy_added
                
                # If SoC is low, bus must return to depot
                if projected_soc < bus.max_soc * self.soc_min_proportion:
                    next_time = last_trip.departure_times[-1] + self._get_deadhead_time_to_depot(
                        last_trip.route_id, bus.home_depot
                    )
                    last_route_id = None  # From depot
                else:
                    next_time = last_trip.departure_times[-1]
                    last_route_id = last_trip.route_id  # From last route
                
                # Calculate remaining SoC
                current_soc = bus.current_soc
                for trip in bus_tour.trips:
                    current_soc -= trip.energy_consumed
                for charging in bus_tour.charging_events:
                    current_soc += charging.energy_added
            
            # Try to add more trips using regret-based selection
            while next_time <= self.planning_horizon:
                # Generate all candidate trips for this bus
                candidates = []
                
                for route_id in self.routes:
                    trip_candidate = self._create_trip_candidate_with_context(
                        route_id, next_time, bus, current_soc, new_solution.passenger_queues, last_route_id
                    )
                    
                    if (trip_candidate and self._is_trip_feasible_enhanced(trip_candidate, bus, current_soc)):
                        passengers_count = sum(len(p) for p in trip_candidate.passengers_boarded)
                        if passengers_count > 0:
                            candidates.append({
                                'trip': trip_candidate,
                                'value': passengers_count,
                                'route_id': route_id
                            })
                
                if not candidates:
                    break  # No feasible candidates
                
                # Calculate regret values: what would we lose if we don't select each option?
                if len(candidates) == 1:
                    # Only one choice - select it
                    selected_candidate = candidates[0]
                else:
                    # Calculate regret for each candidate
                    regret_values = []
                    
                    # Sort candidates by value to find best alternatives
                    candidates.sort(key=lambda x: x['value'], reverse=True)
                    
                    for i, candidate in enumerate(candidates):
                        # Regret = difference between this candidate and the best alternative
                        current_value = candidate['value']
                        
                        # Find best alternative (best among others)
                        alternatives = [c for j, c in enumerate(candidates) if j != i]
                        if alternatives:
                            best_alternative_value = max(alt['value'] for alt in alternatives)
                            regret = current_value - best_alternative_value
                        else:
                            regret = current_value  # No alternatives
                        
                        # Add bonus for high-value trips and amplify regret differences
                        # Higher regret factor makes algorithm more selective about "hard to replace" options
                        regret_factor = 2.0  # Amplify regret differences
                        adjusted_regret = regret * regret_factor + current_value * 0.2
                        
                        regret_values.append({
                            'candidate': candidate,
                            'regret': adjusted_regret
                        })
                    
                    # Select candidate with highest regret (most to lose if not selected)
                    selected_candidate = max(regret_values, key=lambda x: x['regret'])['candidate']
                
                # Insert selected trip
                # Regret: 考虑"错过的损失"最大的 trip
                best_trip = selected_candidate['trip']
                bus_tour.trips.append(best_trip)
                current_soc -= best_trip.energy_consumed
                
                # Determine next state based on charging needs
                needs_charging = current_soc < bus.max_soc * self.soc_min_proportion
                
                if needs_charging:
                    # Must return to depot for charging
                    next_time = best_trip.departure_times[-1] + self._get_deadhead_time_to_depot(
                        best_trip.route_id, bus.home_depot
                    )
                    last_route_id = None  # Next trip from depot
                    
                    charging_event = self._plan_charging(bus, current_soc, next_time)
                    if charging_event:
                        bus_tour.charging_events.append(charging_event)
                        current_soc += charging_event.energy_added
                        next_time = charging_event.end_time
                    else:
                        break  # Can't charge, stop adding trips
                else:
                    # Can continue from current route endpoint
                    next_time = best_trip.departure_times[-1]
                    last_route_id = best_trip.route_id  # Next trip from this route
            
            # Update final return time
            if bus_tour.trips:
                bus_tour.final_return_time = bus_tour.trips[-1].departure_times[-1] + \
                    self._get_deadhead_time_to_depot(bus_tour.trips[-1].route_id, bus.home_depot)
        
        self._evaluate_solution(new_solution)
        return new_solution
    
    def _time_aware_greedy_insertion(self, solution: Solution) -> Solution:
        """Enhanced greedy insertion with departure time optimization"""
        new_solution = solution.copy()

        # 保持现有的乘客队列状态，不要重新创建
        # new_solution.passenger_queues 已经在 copy() 中正确复制了
        self._update_passenger_queues_after_solution(new_solution)
        
        for bus in self.buses:
            bus_tour = new_solution.bus_tours[bus.id]
            
            # Calculate current state
            current_soc = bus.current_soc
            for trip in bus_tour.trips:
                current_soc -= trip.energy_consumed
            for charging in bus_tour.charging_events:
                current_soc += charging.energy_added
            
            # Try to insert new trips with time optimization
            while True:
                best_insertion = None
                best_value = 0
                
                for route_id in self.routes:
                    # Calculate feasible time window
                    # 计算可行时间窗
                    earliest_time, latest_time = self._calculate_feasible_time_window(
                        bus_tour, route_id, bus, current_soc
                    )
                    
                    if earliest_time >= latest_time:
                        continue
                    
                    # Find optimal departure time within window
                    #* 在时间窗内寻找最优出发时间
                    optimal_trip = self._create_trip_candidate_with_time_optimization(
                        route_id, earliest_time, latest_time, bus, current_soc,
                        new_solution.passenger_queues
                    )
                    # 评估并选择最佳trip
                    if optimal_trip:
                        passenger_value = sum(len(p) for p in optimal_trip.passengers_boarded)
                        
                        if passenger_value > best_value:
                            best_insertion = {
                                'trip': optimal_trip,
                                'value': passenger_value,
                                'route_id': route_id
                            }
                            best_value = passenger_value
                
                # Insert best trip if found
                if best_insertion and best_value > 0:
                    trip = best_insertion['trip']
                    bus_tour.trips.append(trip)
                    
                    # Update state
                    current_soc -= trip.energy_consumed
                    
                    # Handle charging if needed
                    if current_soc < bus.max_soc * self.soc_min_proportion:
                        charging_event = self._plan_charging(bus, current_soc, 
                                                        trip.departure_times[-1])
                        if charging_event:
                            bus_tour.charging_events.append(charging_event)
                            current_soc += charging_event.energy_added
                else:
                    break
            
            # Update final return time
            if bus_tour.trips:
                last_trip = bus_tour.trips[-1]
                deadhead_time = self._get_deadhead_time_to_depot(last_trip.route_id, bus.home_depot)
                bus_tour.final_return_time = last_trip.departure_times[-1] + deadhead_time
        
        self._evaluate_solution(new_solution)
        return new_solution
    
    def _time_shift_insertion(self, solution: Solution) -> Solution:
        """
        Insert trips with time-shift optimization
        支持在现有trips之间插入新trip，而不仅仅是在末尾添加。
        """
        new_solution = solution.copy()
        
        # Recreate passenger queues
        new_solution.passenger_queues = copy.deepcopy(self.initial_passenger_queues)
        self._update_passenger_queues_after_solution(new_solution)
        
        # For each bus, try to insert trips at optimal times
        for bus in self.buses:
            bus_tour = new_solution.bus_tours[bus.id]
            
            # Create time slots for potential insertions
            # 1️⃣ 生成所有可能的插入时间槽
            time_slots = self._generate_insertion_time_slots(bus_tour)
            
            for slot in time_slots:
                # 2️⃣ 为每个时间槽寻找最佳路线
                best_route = None
                best_passengers = 0
                
                for route_id in self.routes:
                    # Try inserting trip at this time slot
                    trip_candidate = self._create_trip_candidate_with_context(
                        route_id, slot['time'], bus, slot['soc'],
                        new_solution.passenger_queues
                    )
                    
                    if trip_candidate and self._is_trip_feasible_enhanced(trip_candidate, bus, slot['soc']):
                        passenger_count = sum(len(p) for p in trip_candidate.passengers_boarded)
                        
                        if passenger_count > best_passengers:
                            best_route = route_id
                            best_passengers = passenger_count
                
                # Insert best trip if profitable
                if best_route and best_passengers > 0:
                    optimal_trip = self._create_trip_candidate_with_context(
                        best_route, slot['time'], bus, slot['soc'],
                        new_solution.passenger_queues
                    )
                    
                    # Insert trip at correct position
                    self._insert_trip_at_time_slot(bus_tour, optimal_trip, slot)
        
        self._evaluate_solution(new_solution)
        return new_solution

    def _generate_insertion_time_slots(self, bus_tour: BusTour) -> List[Dict]:
        """
        Generate potential time slots for trip insertion
        ✅ 灵活插入: 不仅在末尾添加，可以在任何合适位置插入
        ✅ 时间精确: 每15分钟一个时间槽，精确控制插入时机
        ✅ 状态准确: 为每个插入位置计算准确的SoC状态
        """
        slots = []
        bus = self.buses[bus_tour.bus_id]  # Get the Bus object

        if not bus_tour.trips:
            # Empty tour: can start anytime
            # 空行程：任何时间都可以开始
            for t in range(0, int(self.planning_horizon), 15):
                slots.append({'time': float(t), 'soc': bus.max_soc, 'position': 0})
        else:
            # Between existing trips
            # 有行程：在trips之间找插入点
            for i in range(len(bus_tour.trips) + 1):
                if i == 0: 
                    # Before first trip
                    latest_time = bus_tour.trips[0].departure_time - 30
                    for t in range(0, int(latest_time), 15):
                        slots.append({'time': float(t), 'soc': bus.max_soc, 'position': i})
                elif i == len(bus_tour.trips):
                    # After last trip
                    earliest_time = bus_tour.trips[-1].departure_times[-1] + 15
                    for t in range(int(earliest_time), int(self.planning_horizon), 15):
                        current_soc = self._calculate_soc_at_position(bus_tour, i)
                        slots.append({'time': float(t), 'soc': current_soc, 'position': i})
                else:
                    # Between trips
                    earliest_time = bus_tour.trips[i-1].departure_times[-1] + 15
                    latest_time = bus_tour.trips[i].departure_time - 30
                    
                    if earliest_time < latest_time:
                        for t in range(int(earliest_time), int(latest_time), 15):
                            current_soc = self._calculate_soc_at_position(bus_tour, i)
                            slots.append({'time': float(t), 'soc': current_soc, 'position': i})
        
        return slots

    def _calculate_soc_at_position(self, bus_tour: BusTour, position: int) -> float:
        """Calculate SoC at a specific position in the bus tour"""
        bus = self.buses[bus_tour.bus_id]
        current_soc = bus.max_soc

        # Subtract energy consumed by trips up to the position
        for i in range(min(position, len(bus_tour.trips))):
            current_soc -= bus_tour.trips[i].energy_consumed

        # Add energy from charging events that occurred before this position
        for charging_event in bus_tour.charging_events:
            # Check if charging event occurred before this position
            if position == 0:
                # Before first trip - no charging events should apply
                continue
            elif position <= len(bus_tour.trips):
                # Between trips or after last trip
                if position == len(bus_tour.trips):
                    # After last trip - all charging events apply
                    current_soc += charging_event.energy_added
                else:
                    # Between trips - check if charging occurred before this position
                    if charging_event.start_time <= bus_tour.trips[position-1].departure_times[-1]:
                        current_soc += charging_event.energy_added

        return max(bus.min_soc, current_soc)

    def _estimate_trip_duration(self, route_id: int) -> float:
        """Estimate the duration of a trip on the given route"""
        try:
            route = self.routes[route_id]
            if not route.stops:
                return 30.0  # Default duration

            # Sum travel times between stops
            total_travel_time = sum(route.travel_times)

            # Add estimated dwell times (assume average 2 passengers per stop)
            num_stops = len(route.stops)
            estimated_dwell_time = num_stops * (self.base_time + 2 * self.boarding_time_per_person)

            total_duration = total_travel_time + estimated_dwell_time
            return max(15.0, total_duration)  # Minimum 15 minutes

        except Exception as e:
            print(f"Error estimating trip duration for route {route_id}: {e}")
            return 30.0  # Default fallback

    def _estimate_charging_time(self, bus: Bus, current_soc: float) -> float:
        """Estimate charging time needed to reach acceptable SoC level"""
        try:
            depot = next(d for d in self.depots if d.id == bus.home_depot)

            # Calculate energy needed to reach 80% SoC
            target_soc = bus.max_soc * 0.8
            energy_needed = max(0, target_soc - current_soc)

            if energy_needed <= 0:
                return 0.0

            # Use fastest available charger for estimation
            fastest_power = 0
            for charger_type in [ChargerType.TYPE_C, ChargerType.TYPE_B, ChargerType.TYPE_A]:
                if charger_type in depot.chargers and depot.chargers[charger_type] > 0:
                    power = depot.charging_power[charger_type]
                    if power > fastest_power:
                        fastest_power = power

            if fastest_power > 0:
                charging_time = energy_needed / fastest_power * 60  # Convert to minutes
                return max(10.0, charging_time)  # Minimum 10 minutes
            else:
                return 60.0  # Default if no chargers available

        except Exception as e:
            print(f"Error estimating charging time for bus {bus.id}: {e}")
            return 60.0  # Default fallback

    def _insert_trip_at_time_slot(self, bus_tour: BusTour, trip: Trip, slot: Dict):
        """Insert a trip at the specified time slot position"""
        position = slot['position']
        target_time = slot['time']

        # Adjust trip departure time to match the slot time
        time_adjustment = target_time - trip.departure_time

        # Update all times in the trip
        trip.departure_time = target_time
        trip.arrival_times = [t + time_adjustment for t in trip.arrival_times]
        trip.departure_times = [t + time_adjustment for t in trip.departure_times]

        # Insert trip at the correct position
        if position >= len(bus_tour.trips):
            # Append at the end
            bus_tour.trips.append(trip)
        else:
            # Insert at the specified position
            bus_tour.trips.insert(position, trip)

        # Update final return time if this is the last trip
        if position >= len(bus_tour.trips) - 1:
            bus = self.buses[bus_tour.bus_id]
            deadhead_time = self._get_deadhead_time_to_depot(trip.route_id, bus.home_depot)
            bus_tour.final_return_time = trip.departure_times[-1] + deadhead_time

    def _update_passenger_queues_after_solution(self, solution: Solution):
        """Remove passengers that are already served from queues"""
        served_passengers = set()
        
        # Collect all served passengers
        for bus_tour in solution.bus_tours:
            for trip in bus_tour.trips:
                for passengers_at_stop in trip.passengers_boarded:
                    for passenger in passengers_at_stop:
                        served_passengers.add(passenger.id)
        
        # Remove served passengers from queues
        for stop_id in solution.passenger_queues.queues:
            queue = solution.passenger_queues.queues[stop_id]
            solution.passenger_queues.queues[stop_id] = deque([
                p for p in queue if p.id not in served_passengers
            ])
    
    def _accept_solution(self, current_score: float, new_score: float, temperature: float) -> bool:
        """Determine whether to accept new solution (simulated annealing)"""
        if new_score >= current_score:
            return True
        
        probability = np.exp((new_score - current_score) / temperature)
        return random.random() < probability

        # Count total passengers in solution
        for bus_tour in solution.bus_tours:
            for trip in bus_tour.trips:
                for passengers_at_stop in trip.passengers_boarded:
                    total_passengers += len(passengers_at_stop)

        # Check if passenger count exceeds total available passengers
        if total_passengers > len(self.passengers):
            print(f"Warning: Solution has {total_passengers} passengers but only {len(self.passengers)} available!")
            return False

        return True

    def solve(self) -> Solution:
        """Main ALNS algorithm loop"""
        if self.logger:
            self.logger.log_section("ENHANCED ALNS ALGORITHM EXECUTION")
            self.logger.log("Starting ALNS algorithm for Electric Bus Evacuation Scheduling...")
            self.logger.log("Generating initial solution...")
        else:
            print("Starting ALNS algorithm for Electric Bus Evacuation Scheduling...")
            print("Generating initial solution...")
        
        current_solution = self.generate_initial_solution()
        # current_solution = self.generate_initial_solution_improved()
        # current_solution = self.generate_initial_solution_demand_aware()
        best_solution = current_solution.copy()
        
        if self.logger:
            self.logger.log(f"Initial solution: {current_solution.objective_value} passengers, "
                           f"penalty: {current_solution.penalty_value}")
        else:
            print(f"Initial solution: {current_solution.objective_value} passengers, "
                  f"penalty: {current_solution.penalty_value}")
        
        # ALNS main loop
        temperature = self.temperature_start
        iteration = 0
        
        while iteration < self.max_iterations and temperature > self.temperature_end:
            iteration += 1
            
            # Select destroy and repair operators
            destroy_op = self._select_operator(self.destroy_weights)
            repair_op = self._select_operator(self.repair_weights)
            
            # Determine number of trips to remove (adaptive)
            total_trips = sum(len(tour.trips) for tour in current_solution.bus_tours)
            num_remove = max(1, int(total_trips * random.uniform(0.1, 0.3)))
            
            # Store scores for evaluation
            original_score = current_solution.objective_value - current_solution.penalty_value
            
            # Destroy phase with timing
            destroy_start_time = time.time()
            
            if destroy_op == 'random_trip_removal':
                destroyed_solution = self._random_trip_removal(current_solution, num_remove)
            elif destroy_op == 'route_based_removal':
                destroyed_solution = self._route_based_removal(current_solution, num_remove)
            elif destroy_op == 'time_based_removal':
                destroyed_solution = self._time_based_removal(current_solution, num_remove)
            else:  # time_shift_removal
                destroyed_solution = self._time_shift_removal(current_solution, num_remove)
            
            destroy_runtime = time.time() - destroy_start_time
            self.operator_evaluator.record_destroy_usage(destroy_op, destroy_runtime)
            
            destroyed_score = destroyed_solution.objective_value - destroyed_solution.penalty_value
            
            # Repair phase with timing
            repair_start_time = time.time()
            
            if repair_op == 'time_aware_greedy_insertion':
                new_solution = self._time_aware_greedy_insertion(destroyed_solution)
            elif repair_op == 'time_shift_insertion':
                new_solution = self._time_shift_insertion(destroyed_solution)
            elif repair_op == 'regret_insertion':
                new_solution = self._regret_insertion(destroyed_solution)
            else:  # greedy_insertion
                new_solution = self._greedy_insertion(destroyed_solution)
            
            repair_runtime = time.time() - repair_start_time
            
            # Calculate solution scores (objective - penalty)
            current_score = current_solution.objective_value - current_solution.penalty_value
            new_score = new_solution.objective_value - new_solution.penalty_value
            
            # Record repair operator performance with runtime
            self.operator_evaluator.record_repair_performance(
                repair_op, original_score, new_score, destroyed_score, repair_runtime
            )
            
            # Record combination performance
            combo_improvement = new_score - original_score
            self.operator_evaluator.record_combo_performance(destroy_op, repair_op, combo_improvement)
            
            # Accept or reject new solution
            if self._accept_solution(current_score, new_score, temperature):
                current_solution = new_solution
                
                # Update best solution
                if new_score > (best_solution.objective_value - best_solution.penalty_value):
                    best_solution = new_solution.copy()
                    if self.logger:
                        self.logger.log_solution_found(iteration, best_solution.objective_value, 
                                                     best_solution.penalty_value)
                    else:
                        print(f"Iteration {iteration}: New best solution found! "
                              f"Passengers: {best_solution.objective_value}, "
                              f"Penalty: {best_solution.penalty_value}")
            
            # Update operator weights based on performance (adaptive)
            self.destroy_weights, self.repair_weights = self.operator_evaluator.update_weights(
                self.destroy_weights, self.repair_weights
            )
            
            # Cool down temperature
            temperature *= self.alpha
            
            # Progress reporting
            if iteration % 100 == 0:
                if self.logger:
                    self.logger.log_algorithm_progress(iteration, current_score, 
                                                     best_solution.objective_value - best_solution.penalty_value, 
                                                     temperature)
                else:
                    print(f"Iteration {iteration}: Current score: {current_score:.2f}, "
                          f"Best score: {best_solution.objective_value - best_solution.penalty_value:.2f}, "
                          f"Temperature: {temperature:.2f}")
                
                # Show current operator weights
                if iteration % 200 == 0:
                    if self.logger:
                        self.logger.log_operator_weights(self.destroy_weights, self.repair_weights)
                    else:
                        print(f"  Destroy weights: {self._format_weights(self.destroy_weights)}")
                        print(f"  Repair weights: {self._format_weights(self.repair_weights)}")
        
        if self.logger:
            self.logger.log_subsection("ALGORITHM COMPLETION")
            self.logger.log(f"ALNS completed after {iteration} iterations.")
            self.logger.log(f"Best solution: {best_solution.objective_value} passengers evacuated")
            self.logger.log(f"Solution penalty: {best_solution.penalty_value}")
            self.logger.log(f"Feasible: {best_solution.is_feasible}")
            self.logger.log(f"Vehicle Usage Number: {best_solution.vehicle_usage_penalty/50:.0f}")
            self.logger.log(f"Time Violations: {best_solution.time_violations}")
            self.logger.log(f"SoC Violations: {best_solution.soc_violations}")

            self.logger.log_subsection("OPERATOR PERFORMANCE REPORT")
        else:
            print(f"\nALNS completed after {iteration} iterations.")
            print(f"Best solution: {best_solution.objective_value} passengers evacuated")
            print(f"Solution penalty: {best_solution.penalty_value}")
            print(f"Feasible: {best_solution.is_feasible}")
            print(f"🚗 Vehicle Usage Number {best_solution.vehicle_usage_penalty/50:.0f}")
            print(f"⏰ Time Violations: {best_solution.time_violations}")
            print(f"🔋 SoC Violations: {best_solution.soc_violations}")
        
        # Print final operator performance report
        self.operator_evaluator.print_performance_report()
        
        return best_solution
    
    def _select_operator(self, weights: Dict[str, float]) -> str:
        """Select operator based on weights (roulette wheel selection)"""
        operators = list(weights.keys())
        probabilities = list(weights.values())
        total_weight = sum(probabilities)
        probabilities = [p / total_weight for p in probabilities]
        
        return np.random.choice(operators, p=probabilities)
    
    def _format_weights(self, weights: Dict[str, float]) -> str:
        """Format weights dictionary for display"""
        formatted = {k: f"{v:.3f}" for k, v in weights.items()}
        return str(formatted)

def load_depot_data_from_excel(filename: str = "depot_information_random.xlsx") -> Tuple[List[Dict], List[int]]:
    """
    从Excel文件中读取depot信息
    
    Args:
        filename: Excel文件名
        
    Returns:
        Tuple[List[Dict], List[int]]: (depot信息列表, 每个depot的车辆数量列表)
    """
    # 检查文件是否存在
    if not os.path.exists(filename):
        print(f"⚠️  文件 {filename} 不存在，使用默认配置")
        return None, None
    
    try:
        # 读取Excel文件
        df = pd.read_excel(filename, sheet_name='Depot_Info')
        print(f"✅ 成功从 {filename} 读取 {len(df)} 个depot的信息")
        
        depot_data_list = []
        vehicle_counts = []
        
        for _, row in df.iterrows():
            # 构建depot信息字典
            depot_info = {
                'id': int(row['depot_id']),
                'location': (float(row['x_coordinate']), float(row['y_coordinate'])),
                'chargers': {
                    ChargerType.TYPE_A: int(row['charger_type_a_count']),
                    ChargerType.TYPE_B: int(row['charger_type_b_count']),
                    ChargerType.TYPE_C: int(row['charger_type_c_count'])
                },
                'charging_power': {
                    ChargerType.TYPE_A: float(row['charger_type_a_power']),
                    ChargerType.TYPE_B: float(row['charger_type_b_power']),
                    ChargerType.TYPE_C: float(row['charger_type_c_power'])
                }
            }
            depot_data_list.append(depot_info)
            vehicle_counts.append(int(row['vehicle_count']))
        
        return depot_data_list, vehicle_counts
        
    except Exception as e:
        print(f"❌ 读取Excel文件失败: {str(e)}")
        print("使用默认配置")
        return None, None

def load_route_data_from_excel(filename: str = "route_information_example.xlsx", travel_speed: float = 20.0) -> Tuple[List[Route], Dict[int, List[Stop]]]:
    """
    从Excel文件中读取路线信息并创建Route对象
    
    Args:
        filename: Excel文件名
        
    Returns:
        Tuple[List[Route], Dict[int, List[Stop]]]: (路线列表, 按route_id分组的站点字典)
    """
    # 检查文件是否存在
    if not os.path.exists(filename):
        print(f"⚠️  文件 {filename} 不存在，使用默认配置")
        return None, None
    
    try:
        # 读取Excel文件的All_Routes工作表
        df = pd.read_excel(filename, sheet_name='All_Routes')
        print(f"✅ 成功从 {filename} 读取 {len(df)} 个站点的路线信息")
        
        # 按route_id分组数据
        routes_dict = {}
        stops_dict = {}
        
        for route_id in df['route_id'].unique():
            route_data = df[df['route_id'] == route_id].sort_values('position_on_route')
            
            # 创建该路线的stops
            stops = []
            travel_times = []
            energy_consumptions = []
            
            for _, row in route_data.iterrows():
                # 创建Stop对象
                stop = Stop(
                    id=int(row['stop_id']),
                    location=(float(row['x_coordinate']), float(row['y_coordinate'])),
                    route_id=int(row['route_id']),
                    position_on_route=int(row['position_on_route'])
                )
                stops.append(stop)
                
                # 计算travel_time (使用travel_distance和平均车速20km/h)
                travel_distance = float(row['travel_distance'])
                if travel_distance > 0:
                    # travel_time = distance / speed * 60 (转换为分钟)
                    travel_time = (travel_distance / travel_speed) * 60.0
                    travel_times.append(travel_time)
                
                # 获取energy_consumption
                energy_consumption = float(row['energy_consumption'])
                if energy_consumption > 0:
                    energy_consumptions.append(energy_consumption)
            
            # 创建Route对象
            route = Route(
                id=int(route_id),
                stops=stops,
                travel_times=travel_times,
                energy_consumption=energy_consumptions
            )
            
            routes_dict[int(route_id)] = route
            stops_dict[int(route_id)] = stops
            
        # 转换为列表形式
        routes = list(routes_dict.values())
        # print(f"✅ 成功创建 {len(routes)} 条路线")
        
        # 打印路线汇总信息
        # for route in routes:
        #     print(f"  📍 Route {route.id}: {len(route.stops)} 站点, "
        #           f"总距离: {sum(route.travel_times) * 20 / 60:.2f} km, "
        #           f"总能耗: {sum(route.energy_consumption):.2f} kWh")
        
        return routes, stops_dict
        
    except Exception as e:
        print(f"❌ 读取路线Excel文件失败: {str(e)}")
        print("使用默认配置")
        return None, None

def load_passenger_data_from_excel(filename: str = "passenger_demand_data.xlsx") -> pd.DataFrame:
    """
    从Excel文件中读取乘客需求数据
    
    Args:
        filename: 乘客需求数据Excel文件名
        
    Returns:
        包含OD需求数据的DataFrame，如果文件不存在返回None
    """
    if not os.path.exists(filename):
        print(f"⚠️  乘客需求文件 {filename} 不存在，将使用随机生成")
        return None
    
    try:
        df = pd.read_excel(filename, sheet_name='OD_Matrix')
        print(f"✅ 成功从 {filename} 读取 {len(df)} 条乘客需求记录")
        return df
    except Exception as e:
        print(f"❌ 读取乘客需求文件失败: {str(e)}")
        return None

def create_passengers_from_excel_data(passenger_demand_df: pd.DataFrame) -> List[Passenger]:
    """
    基于Excel数据创建乘客对象
    
    Args:
        passenger_demand_df: 包含OD需求数据的DataFrame
        
    Returns:
        乘客对象列表
    """
    passengers = []
    passenger_id = 0
    
    print("📊 基于Excel数据生成乘客...")
    
    # 按分钟和OD对生成乘客
    for _, row in passenger_demand_df.iterrows():
        route_id = int(row['route_id'])
        origin_stop = int(row['origin_stop'])
        destination_stop = int(row['destination_stop'])
        minute = int(row['minute'])
        passenger_count = int(row['passenger_count'])
        
        # 为这个时间点的OD对生成指定数量的乘客
        for _ in range(passenger_count):
            passengers.append(Passenger(
                id=passenger_id,
                origin_stop=origin_stop,
                destination_stop=destination_stop,
                arrival_time=float(minute),
                route_id=route_id
            ))
            passenger_id += 1
    
    print(f"✅ 基于Excel数据生成了 {len(passengers)} 名乘客")
    return passengers

def create_passengers_random_fallback(routes: List[Route], time_horizon: int = 180) -> List[Passenger]:
    """
    随机生成乘客（作为Excel数据不可用时的备选方案）
    """
    passengers = []
    passenger_id = 0
    
    print("🎲 使用随机方式生成乘客...")
    
    # 原有的随机生成逻辑
    arrival_counts = [0, 1, 2, 3, 4, 5]
    arrival_probabilities = [0.10, 0.40, 0.20, 0.15, 0.10, 0.05]
    arrival_probabilities_last_period = [0.70, 0.30, 0.00, 0.00, 0.00, 0.00]
    
    for route in routes:
        for stop_idx, stop in enumerate(route.stops):
            for minute in range(time_horizon):
                if minute < time_horizon - 60:
                    num_arrivals = np.random.choice(arrival_counts, p=arrival_probabilities)
                else:
                    num_arrivals = np.random.choice(arrival_counts, p=arrival_probabilities_last_period)
                
                for _ in range(num_arrivals):
                    possible_destinations = []
                    for dest_idx in range(stop_idx + 1, len(route.stops)):
                        possible_destinations.append(route.stops[dest_idx].id)
                    
                    if possible_destinations:
                        destination_stop = random.choice(possible_destinations)
                        passengers.append(Passenger(
                            id=passenger_id,
                            origin_stop=stop.id,
                            destination_stop=destination_stop,
                            arrival_time=float(minute),
                            route_id=route.id
                        ))
                        passenger_id += 1
    
    print(f"✅ 随机生成了 {len(passengers)} 名乘客")
    return passengers

# Example usage and testing
def create_example_problem(depot_info_file, route_info_file, passenger_data_file, bus_travel_speed: float = 20.0):
    """Create a small example problem for testing"""
    
    # Load depot data from Excel file
    depot_data_list, vehicle_counts = load_depot_data_from_excel(depot_info_file)
    
    # Create depots from Excel data or fallback to default
    if depot_data_list is not None:
        depots = []
        for depot_data in depot_data_list:
            depot = Depot(
                id=depot_data['id'],
                location=depot_data['location'],
                chargers=depot_data['chargers'],
                charging_power=depot_data['charging_power']
            )
            depots.append(depot)
    
    # Load route data from Excel file
    routes_from_excel, _ = load_route_data_from_excel(route_info_file, bus_travel_speed)

    # Use Excel route data if available, otherwise fallback to default generation
    if routes_from_excel is not None:
        routes = routes_from_excel
        # print(f"✅ 使用从Excel文件加载的 {len(routes)} 条路线")

    # Create buses using vehicle counts from Excel or default values
    bus_num = vehicle_counts  # 从Excel文件读取的每个车场的公交车数量
    buses = []
    for depot_id, num_buses in enumerate(bus_num):
        for _ in range(num_buses):
            buses.append(Bus(id=len(buses), home_depot=depot_id, capacity=60, max_soc=450.0, min_soc=90.0))
    print(f"🚌 创建公交车: {[f'Depot {len(buses)}辆']}")
    
    # Load passenger data from Excel file
    passenger_demand_df = load_passenger_data_from_excel(passenger_data_file)
    
    # Create passengers from Excel data if available, otherwise use random generation
    if passenger_demand_df is not None:
        passengers = create_passengers_from_excel_data(passenger_demand_df)
    else:
        passengers = create_passengers_random_fallback(routes)
    
    return buses, routes, depots, passengers

if __name__ == "__main__":
    start_time = time.time()
    
    # Create example problem
    travel_speed = 20.0  # 公交行驶速度 km/h

    # buses, routes, depots, passengers = create_example_problem(
    #     depot_info_file="depot_information_example.xlsx",
    #     route_info_file="route_information_example.xlsx",
    #     passenger_data_file="passenger_demand_example.xlsx",
    #     bus_travel_speed = travel_speed
    # )
    buses, routes, depots, passengers = create_example_problem(
        depot_info_file="depot_information_random.xlsx",
        route_info_file="route_information_random.xlsx",
        passenger_data_file="passenger_demand_random.xlsx",
        bus_travel_speed = travel_speed,
    )
    
    # Initialize logger
    logger = SolutionLogger("alns_departure")
    logger.set_problem_scale(
        depot_count=len(depots),
        route_count=len(routes),
        passenger_count=len(passengers),
        bus_count=len(buses)
    )
    
    with logger.logging_context():
        # Initialize ALNS solver
        alns = ElectricBusEvacuationALNS(
            buses=buses,
            routes=routes,
            depots=depots,
            passengers=passengers,
            planning_horizon=180.0,  # 3 hours
            return_buffer=60.0,     # 60 minutes buffer
            logger=logger
        )
    

        # Solve the problem
        best_solution = alns.solve()

        end_time = time.time()
        
        # Log detailed solution
        logger.log_section("DETAILED SOLUTION")
        
        buses_used = 0
        total_trips = 0
        
        for i, bus_tour in enumerate(best_solution.bus_tours):
            # Only log bus tours that have executed trips
            if len(bus_tour.trips) == 0:
                continue
                
            buses_used += 1
            total_trips += len(bus_tour.trips)
                
            # Calculate battery state for this bus
            bus = buses[i]
            initial_soc = bus.current_soc
            
            # Calculate final SoC after all trips and charging
            current_soc = initial_soc
            for trip in bus_tour.trips:
                current_soc -= trip.energy_consumed
            for charging_event in bus_tour.charging_events:
                current_soc += charging_event.energy_added
            
            final_soc = current_soc
            soc_change = final_soc - initial_soc
            
            # Prepare trip details
            trips_info = []
            trip_soc = initial_soc
            for j, trip in enumerate(bus_tour.trips):
                passengers_picked = sum(len(p) for p in trip.passengers_boarded)
                trip_soc -= trip.energy_consumed
                trips_info.append({
                    'route_id': trip.route_id,
                    'departure_time': trip.departure_time,
                    'passengers': passengers_picked,
                    'energy': trip.energy_consumed,
                    'soc_after': trip_soc
                })
            
            # Log bus schedule
            logger.log_bus_schedule(
                bus_id=i,
                initial_soc=initial_soc,
                final_soc=final_soc,
                soc_change=soc_change,
                max_soc=bus.max_soc,
                num_trips=len(bus_tour.trips),
                num_charging=len(bus_tour.charging_events),
                return_time=bus_tour.final_return_time,
                trips_info=trips_info
            )
            
            # Show charging events if any
            if bus_tour.charging_events:
                total_charged = sum(event.energy_added for event in bus_tour.charging_events)
                logger.log(f"  ⚡ Total energy charged: {total_charged:.2f} kWh")
        
        # Log final summary
        execution_time = end_time - start_time
        logger.log_final_summary(
            total_passengers=best_solution.objective_value,
            total_penalty=best_solution.penalty_value,
            is_feasible=best_solution.is_feasible,
            execution_time=execution_time,
            buses_used=buses_used,
            total_trips=total_trips
        )
