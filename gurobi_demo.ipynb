{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import gurobipy as gp\n", "from gurobipy import GRB"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Gurobi Optimizer version 12.0.2 build v12.0.2rc0 (mac64[arm] - Darwin 24.2.0 24C101)\n", "\n", "CPU model: Apple M2\n", "Thread count: 8 physical cores, 8 logical processors, using up to 8 threads\n", "\n", "Optimize a model with 2 rows, 2 columns and 4 nonzeros\n", "Model fingerprint: 0xd89cc51a\n", "Variable types: 1 continuous, 1 integer (0 binary)\n", "Coefficient statistics:\n", "  Matrix range     [1e+00, 2e+00]\n", "  Objective range  [3e+00, 4e+00]\n", "  Bounds range     [0e+00, 0e+00]\n", "  RHS range        [6e+00, 8e+00]\n", "Found heuristic solution: objective 32.0000000\n", "Presolve time: 0.00s\n", "Presolved: 2 rows, 2 columns, 4 nonzeros\n", "Variable types: 0 continuous, 2 integer (0 binary)\n", "\n", "Root relaxation: objective 1.533333e+01, 1 iterations, 0.01 seconds (0.00 work units)\n", "\n", "    Nodes    |    Current Node    |     Objective Bounds      |     Work\n", " Expl Unexpl |  Obj  Depth IntInf | Incumbent    BestBd   Gap | It/Node Time\n", "\n", "     0     0   15.33333    0    2   32.00000   15.33333  52.1%     -    0s\n", "H    0     0                      16.0000000   15.33333  4.17%     -    0s\n", "     0     0   15.33333    0    2   16.00000   15.33333  4.17%     -    0s\n", "\n", "Explored 1 nodes (1 simplex iterations) in 0.04 seconds (0.00 work units)\n", "Thread count was 8 (of 8 available processors)\n", "\n", "Solution count 2: 16 32 \n", "\n", "Optimal solution found (tolerance 1.00e-04)\n", "Best objective 1.600000000000e+01, best bound 1.600000000000e+01, gap 0.0000%\n", "Optimal solution: x = 4.0, y = 1.0\n", "Optimal objective value: 16.0\n"]}], "source": ["from gurobipy import Model, GRB\n", "\n", "# 创建模型\n", "m = Model(\"MILP_example\")\n", "\n", "# 添加变量\n", "x = m.addVar(vtype=GRB.INTEGER, name=\"x\")\n", "y = m.addVar(vtype=GRB.CONTINUOUS, name=\"y\")\n", "\n", "# 设置目标函数：minimize 3x + 4y\n", "m.setObjective(3 * x + 4 * y, GRB.MINIMIZE)\n", "\n", "# 添加约束条件\n", "m.addConstr(2 * x + y >= 8, \"c1\")\n", "m.addConstr(x + 2 * y >= 6, \"c2\")\n", "\n", "# 求解\n", "m.optimize()\n", "\n", "# 输出结果\n", "if m.status == GRB.OPTIMAL:\n", "    print(f\"Optimal solution: x = {x.X}, y = {y.X}\")\n", "    print(f\"Optimal objective value: {m.ObjVal}\")\n", "else:\n", "    print(\"No optimal solution found.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}