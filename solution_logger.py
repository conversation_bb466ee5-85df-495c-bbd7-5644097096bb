#!/usr/bin/env python3
"""
统一的解决方案日志记录器
用于记录ALNS算法的所有输出信息到文件
"""

import os
import sys
import datetime
from typing import List, Dict, Any, Optional, TextIO
from contextlib import contextmanager


class SolutionLogger:
    """统一的解决方案日志记录器"""
    
    def __init__(self, algorithm_type: str = "alns"):
        """
        初始化日志记录器
        
        Args:
            algorithm_type: 算法类型 ("alns" 或 "alns_departure")
        """
        self.algorithm_type = algorithm_type
        self.folder_name = f"sol_{algorithm_type}"
        self.log_file: Optional[TextIO] = None
        self.log_filename: Optional[str] = None
        
        # 问题规模信息
        self.depot_count = 0
        self.route_count = 0
        self.passenger_count = 0
        self.bus_count = 0
        
        # 求解开始时间
        self.start_time = datetime.datetime.now()
        
        # 存储所有输出内容
        self.log_buffer = []
        
    def set_problem_scale(self, depot_count: int, route_count: int, 
                         passenger_count: int, bus_count: int):
        """设置问题规模信息"""
        self.depot_count = depot_count
        self.route_count = route_count
        self.passenger_count = passenger_count
        self.bus_count = bus_count
        
    def _generate_filename(self) -> str:
        """生成日志文件名"""
        end_time = datetime.datetime.now()
        time_str = end_time.strftime("%Y%m%d_%H%M%S")
        
        filename = f"Solution_{self.depot_count}_{self.route_count}_{self.passenger_count}_{time_str}.txt"
        return os.path.join(self.folder_name, filename)
    
    def start_logging(self):
        """开始日志记录"""
        # 确保文件夹存在
        os.makedirs(self.folder_name, exist_ok=True)
        
        # 生成文件名但先不创建文件
        self.log_filename = self._generate_filename()
        
        # 记录开始信息
        self.log("="*80)
        self.log(f"ELECTRIC BUS EVACUATION SCHEDULING - {self.algorithm_type.upper()}")
        self.log("="*80)
        self.log(f"Execution started at: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        self.log("")
        
        # 记录问题规模
        self.log("📊 PROBLEM SCALE:")
        self.log(f"  • Number of Depots: {self.depot_count}")
        self.log(f"  • Number of Routes: {self.route_count}")
        self.log(f"  • Number of Passengers: {self.passenger_count}")
        self.log(f"  • Number of Buses: {self.bus_count}")
        self.log("")
        
    def log(self, message: str = ""):
        """记录一条消息"""
        # 同时输出到控制台和缓存
        print(message)
        self.log_buffer.append(message + "\n")
        
    def log_section(self, title: str, content: str = ""):
        """记录一个章节"""
        self.log()
        self.log("="*60)
        self.log(title)
        self.log("="*60)
        if content:
            self.log(content)
        self.log()
    
    def log_subsection(self, title: str, content: str = ""):
        """记录一个子章节"""
        self.log()
        self.log("-"*40)
        self.log(title)
        self.log("-"*40)
        if content:
            self.log(content)
        self.log()
    
    def log_algorithm_progress(self, iteration: int, current_score: float, 
                             best_score: float, temperature: float):
        """记录算法进度"""
        self.log(f"Iteration {iteration}: Current score: {current_score:.2f}, "
                f"Best score: {best_score:.2f}, Temperature: {temperature:.2f}")
    
    def log_operator_weights(self, destroy_weights: Dict, repair_weights: Dict):
        """记录算子权重"""
        self.log("  Current Operator Weights:")
        self.log(f"    Destroy: {self._format_weights(destroy_weights)}")
        self.log(f"    Repair: {self._format_weights(repair_weights)}")
    
    def log_solution_found(self, iteration: int, passengers: int, penalty: float):
        """记录找到新的最优解"""
        self.log(f"Iteration {iteration}: New best solution found! "
                f"Passengers: {passengers}, Penalty: {penalty}")
    
    def log_bus_schedule(self, bus_id: int, initial_soc: float, final_soc: float, 
                        soc_change: float, max_soc: float, num_trips: int, 
                        num_charging: int, return_time: float, trips_info: List[Dict]):
        """记录公交车调度详情"""
        self.log(f"\nBus {bus_id} Schedule:")
        self.log(f"  🔋 Initial SoC: {initial_soc:.2f} kWh ({initial_soc/max_soc*100:.1f}%)")
        self.log(f"  🔋 Final SoC: {final_soc:.2f} kWh ({final_soc/max_soc*100:.1f}%)")
        self.log(f"  🔋 SoC Change: {soc_change:+.2f} kWh")
        self.log(f"  🚌 Number of trips: {num_trips}")
        self.log(f"  ⚡ Number of charging events: {num_charging}")
        self.log(f"  ⏰ Final return time: {return_time:.2f} min")
        
        # 详细行程信息
        if trips_info:
            self.log("  📋 Trip Details:")
            for i, trip in enumerate(trips_info, 1):
                self.log(f"    Trip {i}: Route {trip['route_id']}, "
                        f"Departure: {trip['departure_time']:.2f} min, "
                        f"Passengers: {trip['passengers']}, "
                        f"Energy: {trip['energy']:.2f} kWh, "
                        f"SoC after trip: {trip['soc_after']:.2f} kWh")
    
    def log_final_summary(self, total_passengers: int, total_penalty: float, 
                         is_feasible: bool, execution_time: float, 
                         buses_used: int, total_trips: int):
        """记录最终总结"""
        self.log_section("FINAL SOLUTION SUMMARY")
        self.log(f"📊 Solution Quality:")
        self.log(f"  • Total Passengers Evacuated: {total_passengers}")
        self.log(f"  • Total Penalty: {total_penalty:.2f}")
        self.log(f"  • Solution Feasible: {'✅ Yes' if is_feasible else '❌ No'}")
        self.log(f"  • Buses Used: {buses_used}/{self.bus_count}")
        self.log(f"  • Total Trips: {total_trips}")
        self.log(f"  • Bus Utilization: {buses_used/self.bus_count*100:.1f}%")
        self.log()
        self.log(f"⏱️  Execution Time: {execution_time:.2f} seconds")
        
        end_time = datetime.datetime.now()
        self.log(f"📅 Execution completed at: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        self.log(f"🕐 Total Runtime: {(end_time - self.start_time).total_seconds():.2f} seconds")
    
    def _format_weights(self, weights: Dict[str, float]) -> str:
        """格式化权重字典显示"""
        formatted = {k: f"{v:.3f}" for k, v in weights.items()}
        return str(formatted)
    
    def save_and_close(self):
        """保存日志到文件并关闭"""
        if not self.log_filename:
            self.log_filename = self._generate_filename()
        
        # 确保文件夹存在
        os.makedirs(os.path.dirname(self.log_filename), exist_ok=True)
        
        # 写入所有缓存的内容到文件
        with open(self.log_filename, 'w', encoding='utf-8') as f:
            f.writelines(self.log_buffer)
        
        print(f"\n💾 Log saved to: {self.log_filename}")
    
    @contextmanager
    def logging_context(self):
        """日志记录上下文管理器"""
        try:
            self.start_logging()
            yield self
        finally:
            self.save_and_close()


class TeeOutput:
    """同时输出到控制台和日志的类"""
    
    def __init__(self, logger: SolutionLogger):
        self.logger = logger
        self.original_stdout = sys.stdout
        
    def write(self, text):
        # 输出到原始stdout（控制台）
        self.original_stdout.write(text)
        # 也添加到日志缓存（但不重复添加换行）
        if text.strip():  # 只记录非空内容
            self.logger.log_buffer.append(text)
    
    def flush(self):
        self.original_stdout.flush()


# 示例使用方法
if __name__ == "__main__":
    # 基本使用示例
    logger = SolutionLogger("alns")
    logger.set_problem_scale(depot_count=3, route_count=5, passenger_count=1500, bus_count=20)
    
    with logger.logging_context():
        logger.log_section("ALGORITHM INITIALIZATION")
        logger.log("Starting ALNS algorithm...")
        logger.log("Generating initial solution...")
        
        logger.log_section("ALGORITHM PROGRESS")
        for i in range(1, 6):
            logger.log_algorithm_progress(i, 1500.0 - i*10, 1500.0, 100.0 - i*5)
        
        logger.log_section("OPERATOR PERFORMANCE")
        logger.log("Destroy operators performance analysis...")
        
        logger.log_final_summary(
            total_passengers=1450,
            total_penalty=50.0,
            is_feasible=True,
            execution_time=45.6,
            buses_used=18,
            total_trips=85
        )
