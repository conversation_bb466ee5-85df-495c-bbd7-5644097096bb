#!/usr/bin/env python3
"""
Depot Data Generator
生成depot信息的Excel文件，包含depot编号、坐标、充电桩信息和车辆数量
"""

import pandas as pd
import numpy as np
import random
from enum import Enum
from typing import Dict, List, Tuple
import os

class ChargerType(Enum):
    """充电桩类型"""
    TYPE_A = "A"
    TYPE_B = "B" 
    TYPE_C = "C"

def generate_depot_data(num_depots: int = 5, area_size: Tuple[float, float] = (100.0, 100.0)) -> pd.DataFrame:
    """
    生成depot数据
    
    Args:
        num_depots: 生成的depot数量
        area_size: 区域大小 (width, height)
    
    Returns:
        包含depot信息的DataFrame
    """
    
    # 设置随机种子以确保可重现性
    random.seed(42)
    np.random.seed(42)
    
    depot_data = []
    
    for depot_id in range(num_depots):
        # 生成随机坐标
        x_coord = round(random.uniform(0, area_size[0]), 2)
        y_coord = round(random.uniform(0, area_size[1]), 2)
        
        # 生成充电桩数量（根据depot规模随机分配）
        # 小型depot: 2-5个TYPE_A, 1-3个TYPE_B, 0-1个TYPE_C
        # 中型depot: 4-8个TYPE_A, 2-5个TYPE_B, 1-2个TYPE_C
        # 大型depot: 6-12个TYPE_A, 3-8个TYPE_B, 1-3个TYPE_C
        
        depot_scale = random.choice(['small', 'medium', 'large'])
        
        if depot_scale == 'small':
            type_a_count = random.randint(2, 5)
            type_b_count = random.randint(1, 3)
            type_c_count = random.randint(0, 1)
            vehicle_count = random.randint(8, 15)
        elif depot_scale == 'medium':
            type_a_count = random.randint(4, 8)
            type_b_count = random.randint(2, 5)
            type_c_count = random.randint(1, 2)
            vehicle_count = random.randint(15, 25)
        else:  # large
            type_a_count = random.randint(6, 12)
            type_b_count = random.randint(3, 8)
            type_c_count = random.randint(1, 3)
            vehicle_count = random.randint(25, 40)
        
        # 充电桩功率设置（kW）
        # TYPE_A: 慢充 120或180kW
        # TYPE_B: 快充 240或300kW  
        # TYPE_C: 超快充 360、480或600kW
        type_a_power = random.choice([120, 180])
        type_b_power = random.choice([240, 300])
        type_c_power = random.choice([360, 480, 600])
        
        depot_info = {
            'depot_id': depot_id,
            'x_coordinate': x_coord,
            'y_coordinate': y_coord,
            'charger_type_a_count': type_a_count,
            'charger_type_a_power': type_a_power,
            'charger_type_b_count': type_b_count,
            'charger_type_b_power': type_b_power,
            'charger_type_c_count': type_c_count,
            'charger_type_c_power': type_c_power,
            'total_chargers': type_a_count + type_b_count + type_c_count,
            'vehicle_count': vehicle_count,
            'depot_scale': depot_scale
        }
        
        depot_data.append(depot_info)
    
    # 创建DataFrame
    df = pd.DataFrame(depot_data)
    
    # 重新排列列的顺序，使其更加直观
    column_order = [
        'depot_id', 
        'x_coordinate', 
        'y_coordinate',
        'charger_type_a_count',
        'charger_type_a_power',
        'charger_type_b_count', 
        'charger_type_b_power',
        'charger_type_c_count',
        'charger_type_c_power',
        'total_chargers',
        'vehicle_count',
        'depot_scale'
    ]
    
    return df[column_order]

def save_to_excel(df: pd.DataFrame, filename: str = "depot_information.xlsx") -> None:
    """
    将DataFrame保存为Excel文件
    
    Args:
        df: 包含depot信息的DataFrame
        filename: 输出文件名
    """
    
    # 创建Excel writer对象
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        # 写入主要数据
        df.to_excel(writer, sheet_name='Depot_Info', index=False)
        
        # 获取工作表对象进行格式化
        worksheet = writer.sheets['Depot_Info']
        
        # 设置列宽
        column_widths = {
            'A': 12,  # depot_id
            'B': 15,  # x_coordinate  
            'C': 15,  # y_coordinate
            'D': 20,  # charger_type_a_count
            'E': 20,  # charger_type_a_power
            'F': 20,  # charger_type_b_count
            'G': 20,  # charger_type_b_power
            'H': 20,  # charger_type_c_count
            'I': 20,  # charger_type_c_power
            'J': 15,  # total_chargers
            'K': 15,  # vehicle_count
            'L': 15   # depot_scale
        }
        
        for col, width in column_widths.items():
            worksheet.column_dimensions[col].width = width
        
        # 创建汇总统计表
        summary_data = {
            '统计项目': [
                '总depot数量',
                '总车辆数量', 
                '总充电桩数量',
                'TYPE_A充电桩总数',
                'TYPE_B充电桩总数', 
                'TYPE_C充电桩总数',
                '平均每个depot车辆数',
                '平均每个depot充电桩数'
            ],
            '数值': [
                len(df),
                df['vehicle_count'].sum(),
                df['total_chargers'].sum(),
                df['charger_type_a_count'].sum(),
                df['charger_type_b_count'].sum(),
                df['charger_type_c_count'].sum(),
                round(df['vehicle_count'].mean(), 1),
                round(df['total_chargers'].mean(), 1)
            ]
        }
        
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name='Summary', index=False)
        
        # 设置汇总表格式
        summary_worksheet = writer.sheets['Summary']
        summary_worksheet.column_dimensions['A'].width = 25
        summary_worksheet.column_dimensions['B'].width = 15

def create_custom_depot_data() -> pd.DataFrame:
    """
    创建自定义的depot数据（基于现有代码的示例）
    """
    
    custom_depots = [
        {
            'depot_id': 0,
            'x_coordinate': 0.0,
            'y_coordinate': 0.0,
            'charger_type_a_count': 3,
            'charger_type_a_power': 180.0,
            'charger_type_b_count': 2,
            'charger_type_b_power': 300.0,
            'charger_type_c_count': 1,
            'charger_type_c_power': 600.0,
            'total_chargers': 6,
            'vehicle_count': 10,
            'depot_scale': 'medium'
        },
        {
            'depot_id': 1,
            'x_coordinate': 50.0,
            'y_coordinate': 30.0,
            'charger_type_a_count': 3,
            'charger_type_a_power': 180.0,
            'charger_type_b_count': 2,
            'charger_type_b_power': 300.0,
            'charger_type_c_count': 1,
            'charger_type_c_power': 600.0,
            'total_chargers': 6,
            'vehicle_count': 10,
            'depot_scale': 'medium'
        },
        {
            'depot_id': 2,
            'x_coordinate': 80.0,
            'y_coordinate': 60.0,
            'charger_type_a_count': 5,
            'charger_type_a_power': 150.0,
            'charger_type_b_count': 3,
            'charger_type_b_power': 280.0,
            'charger_type_c_count': 2,
            'charger_type_c_power': 550.0,
            'total_chargers': 10,
            'vehicle_count': 20,
            'depot_scale': 'large'
        }
    ]
    
    return pd.DataFrame(custom_depots)

def main():
    """主函数"""
    
    print("🚌 电动公交车场数据生成器")
    print("=" * 50)
    
    # 选择生成模式
    mode = input("请选择生成模式 (1: 随机生成, 2: 使用示例数据): ").strip()
    
    if mode == "1":
        # 随机生成模式
        num_depots = int(input("请输入要生成的depot数量 (默认5): ") or "5")
        area_width = float(input("请输入区域宽度 (默认100.0): ") or "100.0")
        area_height = float(input("请输入区域高度 (默认100.0): ") or "100.0")
        
        print(f"\n正在生成 {num_depots} 个depot的随机数据...")
        df = generate_depot_data(num_depots, (area_width, area_height))
        filename = "depot_information_random.xlsx"
        
    else:
        # 使用示例数据
        print("\n使用预设的示例数据...")
        df = create_custom_depot_data()
        filename = "depot_information_example.xlsx"
    
    # 显示生成的数据
    print("\n生成的depot数据:")
    print("-" * 80)
    print(df.to_string(index=False))
    
    # 保存到Excel文件
    print(f"\n正在保存到 {filename}...")
    save_to_excel(df, filename)
    
    print("✅ Excel文件生成完成!")
    print(f"📁 文件位置: {os.path.abspath(filename)}")
    
    # 显示汇总信息
    print("\n📊 数据汇总:")
    print(f"  • 总depot数量: {len(df)}")
    print(f"  • 总车辆数量: {df['vehicle_count'].sum()}")
    print(f"  • 总充电桩数量: {df['total_chargers'].sum()}")
    print(f"  • 平均每个depot车辆数: {df['vehicle_count'].mean():.1f}")

if __name__ == "__main__":
    main()