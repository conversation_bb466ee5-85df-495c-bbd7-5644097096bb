#!/usr/bin/env python3
"""
性能对比脚本 - 在相同问题案例和相同迭代次数下对比不同算法的求解质量和速度
"""

import time
import copy
import sys
import os
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from electric_bus_evacuation_alns import ElectricBusEvacuationALNS, create_example_problem
from electric_bus_evacuation_alns_fast import FastElectricBusEvacuationALNS, create_fast_example_problem
from electric_bus_evacuation_alns_optimized import OptimizedElectricBusEvacuationALNS, create_optimized_example_problem
from electric_bus_evacuation_alns_balanced_fast import BalancedFastALNS, create_balanced_fast_example_problem

class PerformanceComparator:
    """性能对比器"""
    
    def __init__(self):
        self.results = {}
        
    def load_test_case(self, case_name: str, depot_file: str, route_file: str, passenger_file: str) -> Dict[str, Any]:
        """加载测试案例"""
        print(f"\n📂 加载测试案例: {case_name}")
        
        try:
            # 使用原版加载方法
            buses, routes, depots, passengers = create_example_problem(
                depot_info_file=depot_file,
                route_info_file=route_file,
                passenger_data_file=passenger_file
            )
            
            print(f"✅ 成功加载测试案例")
            print(f"   车辆数: {len(buses)}")
            print(f"   路线数: {len(routes)}")
            print(f"   车场数: {len(depots)}")
            print(f"   乘客数: {len(passengers)}")
            
            return {
                'name': case_name,
                'buses': buses,
                'routes': routes,
                'depots': depots,
                'passengers': passengers
            }
            
        except Exception as e:
            print(f"❌ 加载测试案例失败: {str(e)}")
            return None
    
    def run_original_alns(self, test_case: Dict[str, Any], max_iterations: int = 100, 
                         planning_horizon: float = 180.0, return_buffer: float = 60.0) -> Dict[str, Any]:
        """运行原版ALNS"""
        print(f"\n🔄 运行原版ALNS算法...")
        
        start_time = time.time()
        
        # 创建原版ALNS实例
        alns = ElectricBusEvacuationALNS(
            buses=test_case['buses'],
            routes=test_case['routes'],
            depots=test_case['depots'],
            passengers=test_case['passengers'],
            planning_horizon=planning_horizon,
            return_buffer=return_buffer
        )
        
        # 设置迭代次数
        alns.max_iterations = max_iterations
        
        # 记录初始化时间
        init_time = time.time() - start_time
        
        # 运行求解
        solve_start = time.time()
        solution = alns.solve()
        solve_time = time.time() - solve_start
        
        total_time = time.time() - start_time
        
        # 收集结果
        result = {
            'algorithm': 'Original ALNS',
            'total_time': total_time,
            'init_time': init_time,
            'solve_time': solve_time,
            'iterations': max_iterations,
            'passengers_evacuated': solution.total_passengers_evacuated,
            'penalty_value': solution.penalty_value,
            'objective_value': solution.objective_value,
            'final_score': solution.objective_value - solution.penalty_value,
            'is_feasible': solution.is_feasible,
            'avg_iteration_time': solve_time / max_iterations,
            'iteration_speed': max_iterations / solve_time,
            'buses_used': sum(1 for tour in solution.bus_tours if len(tour.trips) > 0),
            'total_trips': sum(len(tour.trips) for tour in solution.bus_tours)
        }
        
        print(f"✅ 原版ALNS完成")
        print(f"   总耗时: {total_time:.2f}秒")
        print(f"   疏散乘客: {result['passengers_evacuated']}")
        print(f"   最终分数: {result['final_score']:.1f}")
        print(f"   可行性: {'✅' if result['is_feasible'] else '❌'}")
        
        return result
    
    def run_fast_alns(self, test_case: Dict[str, Any], max_iterations: int = 100,
                     planning_horizon: float = 180.0, return_buffer: float = 60.0) -> Dict[str, Any]:
        """运行FastALNS"""
        print(f"\n⚡ 运行FastALNS算法...")
        
        start_time = time.time()
        
        # 创建FastALNS实例
        fast_alns = FastElectricBusEvacuationALNS(
            buses=test_case['buses'],
            routes=test_case['routes'],
            depots=test_case['depots'],
            passengers=test_case['passengers'],
            planning_horizon=planning_horizon,
            return_buffer=return_buffer
        )
        
        # 设置迭代次数（确保与原版一致）
        fast_alns.max_iterations = max_iterations
        
        # 记录初始化时间
        init_time = time.time() - start_time
        
        # 运行求解
        solve_start = time.time()
        solution = fast_alns.solve_fast()
        solve_time = time.time() - solve_start
        
        total_time = time.time() - start_time
        
        # 收集结果
        result = {
            'algorithm': 'FastALNS',
            'total_time': total_time,
            'init_time': init_time,
            'solve_time': solve_time,
            'iterations': max_iterations,
            'passengers_evacuated': solution.total_passengers_evacuated,
            'penalty_value': solution.penalty_value,
            'objective_value': solution.objective_value,
            'final_score': solution.objective_value - solution.penalty_value,
            'is_feasible': solution.is_feasible,
            'avg_iteration_time': solve_time / max_iterations,
            'iteration_speed': max_iterations / solve_time,
            'buses_used': sum(1 for tour in solution.bus_tours if len(tour.trips) > 0),
            'total_trips': sum(len(tour.trips) for tour in solution.bus_tours)
        }
        
        print(f"✅ FastALNS完成")
        print(f"   总耗时: {total_time:.2f}秒")
        print(f"   疏散乘客: {result['passengers_evacuated']}")
        print(f"   最终分数: {result['final_score']:.1f}")
        print(f"   可行性: {'✅' if result['is_feasible'] else '❌'}")
        
        return result
    
    def run_optimized_alns(self, test_case: Dict[str, Any], max_iterations: int = 100,
                          planning_horizon: float = 180.0, return_buffer: float = 60.0) -> Dict[str, Any]:
        """运行优化版ALNS"""
        print(f"\n⚡ 运行优化版ALNS算法...")
        
        start_time = time.time()
        
        # 创建优化版ALNS实例
        optimized_alns = OptimizedElectricBusEvacuationALNS(
            buses=test_case['buses'],
            routes=test_case['routes'],
            depots=test_case['depots'],
            passengers=test_case['passengers'],
            planning_horizon=planning_horizon,
            return_buffer=return_buffer
        )
        
        # 设置迭代次数（确保与原版一致）
        optimized_alns.max_iterations = max_iterations
        
        # 记录初始化时间
        init_time = time.time() - start_time
        
        # 运行求解
        solve_start = time.time()
        solution = optimized_alns.solve()
        solve_time = time.time() - solve_start
        
        total_time = time.time() - start_time
        
        # 收集结果
        result = {
            'algorithm': 'OptimizedALNS',
            'total_time': total_time,
            'init_time': init_time,
            'solve_time': solve_time,
            'iterations': max_iterations,
            'passengers_evacuated': solution.total_passengers_evacuated,
            'penalty_value': solution.penalty_value,
            'objective_value': solution.objective_value,
            'final_score': solution.objective_value - solution.penalty_value,
            'is_feasible': solution.is_feasible,
            'avg_iteration_time': solve_time / max_iterations,
            'iteration_speed': max_iterations / solve_time,
            'buses_used': sum(1 for tour in solution.bus_tours if len(tour.trips) > 0),
            'total_trips': sum(len(tour.trips) for tour in solution.bus_tours)
        }
        
        print(f"✅ 优化版ALNS完成")
        print(f"   总耗时: {total_time:.2f}秒")
        print(f"   疏散乘客: {result['passengers_evacuated']}")
        print(f"   最终分数: {result['final_score']:.1f}")
        print(f"   可行性: {'✅' if result['is_feasible'] else '❌'}")
        
        return result
    
    def run_balanced_fast_alns(self, test_case: Dict[str, Any], max_iterations: int = 100,
                              planning_horizon: float = 180.0, return_buffer: float = 60.0) -> Dict[str, Any]:
        """运行平衡版FastALNS"""
        print(f"\n⚡ 运行平衡版FastALNS算法...")
        
        start_time = time.time()
        
        # 创建平衡版FastALNS实例
        balanced_fast_alns = BalancedFastALNS(
            buses=test_case['buses'],
            routes=test_case['routes'],
            depots=test_case['depots'],
            passengers=test_case['passengers'],
            planning_horizon=planning_horizon,
            return_buffer=return_buffer
        )
        
        # 设置迭代次数（确保与原版一致）
        balanced_fast_alns.max_iterations = max_iterations
        
        # 记录初始化时间
        init_time = time.time() - start_time
        
        # 运行求解
        solve_start = time.time()
        solution = balanced_fast_alns.solve()
        solve_time = time.time() - solve_start
        
        total_time = time.time() - start_time
        
        # 收集结果
        result = {
            'algorithm': 'BalancedFastALNS',
            'total_time': total_time,
            'init_time': init_time,
            'solve_time': solve_time,
            'iterations': max_iterations,
            'passengers_evacuated': solution.total_passengers_evacuated,
            'penalty_value': solution.penalty_value,
            'objective_value': solution.objective_value,
            'final_score': solution.objective_value - solution.penalty_value,
            'is_feasible': solution.is_feasible,
            'avg_iteration_time': solve_time / max_iterations,
            'iteration_speed': max_iterations / solve_time,
            'buses_used': sum(1 for tour in solution.bus_tours if len(tour.trips) > 0),
            'total_trips': sum(len(tour.trips) for tour in solution.bus_tours)
        }
        
        print(f"✅ 平衡版FastALNS完成")
        print(f"   总耗时: {total_time:.2f}秒")
        print(f"   疏散乘客: {result['passengers_evacuated']}")
        print(f"   最终分数: {result['final_score']:.1f}")
        print(f"   可行性: {'✅' if result['is_feasible'] else '❌'}")
        
        return result
    
    def compare_algorithms(self, test_case: Dict[str, Any], max_iterations: int = 100,
                          planning_horizon: float = 180.0, return_buffer: float = 60.0, 
                          num_runs: int = 1) -> Dict[str, Any]:
        """对比算法性能"""
        print(f"\n🏁 开始算法性能对比")
        print(f"   测试案例: {test_case['name']}")
        print(f"   最大迭代次数: {max_iterations}")
        print(f"   规划时间: {planning_horizon}分钟")
        print(f"   返回缓冲: {return_buffer}分钟")
        print(f"   运行次数: {num_runs}")
        
        original_results = []
        fast_results = []
        optimized_results = []
        balanced_results = []
        
        for run in range(num_runs):
            print(f"\n{'='*20} 第 {run+1}/{num_runs} 次运行 {'='*20}")
            
            # 运行原版ALNS
            original_result = self.run_original_alns(
                test_case, max_iterations, planning_horizon, return_buffer
            )
            original_results.append(original_result)
            
            # 运行FastALNS
            fast_result = self.run_fast_alns(
                test_case, max_iterations, planning_horizon, return_buffer
            )
            fast_results.append(fast_result)
            
            # 运行优化版ALNS
            optimized_result = self.run_optimized_alns(
                test_case, max_iterations, planning_horizon, return_buffer
            )
            optimized_results.append(optimized_result)
            
            # 运行平衡版FastALNS
            balanced_result = self.run_balanced_fast_alns(
                test_case, max_iterations, planning_horizon, return_buffer
            )
            balanced_results.append(balanced_result)
        
        # 统计分析
        comparison = self._analyze_results(original_results, fast_results, optimized_results, balanced_results)
        
        return comparison
    
    def _analyze_results(self, original_results: List[Dict], fast_results: List[Dict], 
                        optimized_results: List[Dict], balanced_results: List[Dict]) -> Dict[str, Any]:
        """分析对比结果"""
        def calculate_stats(results: List[Dict], key: str):
            values = [r[key] for r in results if key in r]
            if not values:
                return {'mean': 0, 'std': 0, 'min': 0, 'max': 0}
            return {
                'mean': np.mean(values),
                'std': np.std(values),
                'min': np.min(values),
                'max': np.max(values)
            }
        
        # 关键指标统计
        metrics = [
            'total_time', 'passengers_evacuated', 'final_score', 
            'avg_iteration_time', 'iteration_speed', 'buses_used', 'total_trips'
        ]
        
        analysis = {
            'original': {},
            'fast': {},
            'optimized': {},
            'balanced': {},
            'comparison': {}
        }
        
        # 计算统计数据
        for metric in metrics:
            analysis['original'][metric] = calculate_stats(original_results, metric)
            analysis['fast'][metric] = calculate_stats(fast_results, metric)
            analysis['optimized'][metric] = calculate_stats(optimized_results, metric)
            analysis['balanced'][metric] = calculate_stats(balanced_results, metric)
        
        # 计算改进比例
        for metric in metrics:
            orig_mean = analysis['original'][metric]['mean']
            fast_mean = analysis['fast'][metric]['mean']
            optimized_mean = analysis['optimized'][metric]['mean']
            balanced_mean = analysis['balanced'][metric]['mean']
            
            if orig_mean != 0:
                if metric in ['total_time', 'avg_iteration_time']:
                    # 时间指标：越小越好
                    fast_improvement = (orig_mean - fast_mean) / orig_mean * 100
                    optimized_improvement = (orig_mean - optimized_mean) / orig_mean * 100
                    balanced_improvement = (orig_mean - balanced_mean) / orig_mean * 100
                    analysis['comparison'][metric] = {
                        'fast_improvement_percent': fast_improvement,
                        'fast_speedup_factor': orig_mean / fast_mean if fast_mean > 0 else 0,
                        'optimized_improvement_percent': optimized_improvement,
                        'optimized_speedup_factor': orig_mean / optimized_mean if optimized_mean > 0 else 0,
                        'balanced_improvement_percent': balanced_improvement,
                        'balanced_speedup_factor': orig_mean / balanced_mean if balanced_mean > 0 else 0
                    }
                else:
                    # 其他指标：数值比较
                    fast_change = (fast_mean - orig_mean) / orig_mean * 100
                    optimized_change = (optimized_mean - orig_mean) / orig_mean * 100
                    balanced_change = (balanced_mean - orig_mean) / orig_mean * 100
                    analysis['comparison'][metric] = {
                        'fast_change_percent': fast_change,
                        'fast_ratio': fast_mean / orig_mean if orig_mean > 0 else 0,
                        'optimized_change_percent': optimized_change,
                        'optimized_ratio': optimized_mean / orig_mean if orig_mean > 0 else 0,
                        'balanced_change_percent': balanced_change,
                        'balanced_ratio': balanced_mean / orig_mean if orig_mean > 0 else 0
                    }
        
        return analysis
    
    def print_comparison_report(self, analysis: Dict[str, Any], test_case_name: str):
        """打印对比报告"""
        print(f"\n{'='*60}")
        print(f"🏆 算法性能对比报告 - {test_case_name}")
        print(f"{'='*60}")
        
        # 解质量对比
        print(f"\n📊 解质量对比:")
        print(f"{'指标':<18} {'原版ALNS':<12} {'FastALNS':<12} {'优化版':<12} {'平衡版':<12} {'Fast':<8} {'优化':<8} {'平衡':<8}")
        print(f"{'-'*105}")
        
        quality_metrics = ['passengers_evacuated', 'final_score', 'buses_used', 'total_trips']
        for metric in quality_metrics:
            orig = analysis['original'][metric]['mean']
            fast = analysis['fast'][metric]['mean']
            optimized = analysis['optimized'][metric]['mean']
            balanced = analysis['balanced'][metric]['mean']
            fast_change = analysis['comparison'][metric]['fast_change_percent']
            optimized_change = analysis['comparison'][metric]['optimized_change_percent']
            balanced_change = analysis['comparison'][metric]['balanced_change_percent']
            
            metric_name = {
                'passengers_evacuated': '疏散乘客数',
                'final_score': '最终分数',
                'buses_used': '使用车辆数',
                'total_trips': '总行程数'
            }[metric]
            
            print(f"{metric_name:<18} {orig:<12.0f} {fast:<12.0f} {optimized:<12.0f} {balanced:<12.0f} {fast_change:>+6.1f}% {optimized_change:>+6.1f}% {balanced_change:>+6.1f}%")
        
        # 计算性能对比
        print(f"\n⚡ 计算性能对比:")
        print(f"{'指标':<16} {'原版ALNS':<10} {'FastALNS':<10} {'优化版':<10} {'平衡版':<10} {'Fast':<7} {'优化':<7} {'平衡':<7}")
        print(f"{'-'*80}")
        
        performance_metrics = ['total_time', 'avg_iteration_time', 'iteration_speed']
        for metric in performance_metrics:
            orig = analysis['original'][metric]['mean']
            fast = analysis['fast'][metric]['mean']
            optimized = analysis['optimized'][metric]['mean']
            balanced = analysis['balanced'][metric]['mean']
            
            metric_name = {
                'total_time': '总耗时(秒)',
                'avg_iteration_time': '迭代时间(秒)',
                'iteration_speed': '迭代速度(/s)'
            }[metric]
            
            if metric == 'iteration_speed':
                fast_speedup = analysis['comparison'][metric]['fast_ratio']
                optimized_speedup = analysis['comparison'][metric]['optimized_ratio']
                balanced_speedup = analysis['comparison'][metric]['balanced_ratio']
                print(f"{metric_name:<16} {orig:<10.1f} {fast:<10.1f} {optimized:<10.1f} {balanced:<10.1f} {fast_speedup:>5.2f}x {optimized_speedup:>5.2f}x {balanced_speedup:>5.2f}x")
            else:
                fast_speedup = analysis['comparison'][metric]['fast_speedup_factor']
                optimized_speedup = analysis['comparison'][metric]['optimized_speedup_factor']
                balanced_speedup = analysis['comparison'][metric]['balanced_speedup_factor']
                print(f"{metric_name:<16} {orig:<10.2f} {fast:<10.2f} {optimized:<10.2f} {balanced:<10.2f} {fast_speedup:>5.2f}x {optimized_speedup:>5.2f}x {balanced_speedup:>5.2f}x")
        
        # 综合评价
        print(f"\n🎯 综合评价:")
        
        # 平衡版评价
        balanced_score_change = analysis['comparison']['final_score']['balanced_change_percent']
        balanced_passenger_change = analysis['comparison']['passengers_evacuated']['balanced_change_percent']
        balanced_time_speedup = analysis['comparison']['total_time']['balanced_speedup_factor']
        balanced_iter_speedup = analysis['comparison']['avg_iteration_time']['balanced_speedup_factor']
        
        print(f"🏆 平衡版FastALNS表现:")
        print(f"   解质量变化: {balanced_score_change:+.1f}% (分数), {balanced_passenger_change:+.1f}% (乘客)")
        print(f"   计算速度: {balanced_time_speedup:.2f}x (总时间), {balanced_iter_speedup:.2f}x (迭代)")
        
        # 算法推荐
        if abs(balanced_score_change) <= 2 and balanced_time_speedup >= 1.1:
            print(f"   🎯 推荐: 平衡版FastALNS - 质量稳定且有速度提升")
        elif abs(balanced_score_change) <= 5:
            print(f"   ⚖️  平衡版FastALNS - 质量与速度平衡较好")
        else:
            print(f"   ⚠️  平衡版FastALNS - 需要进一步优化")
        
        # 效率评价（已移除旧代码）
        print(f"\n📈 算法选择建议:")
        print(f"   🥇 最佳解质量: 平衡版FastALNS ({balanced_score_change:+.1f}%)")
        print(f"   ⚡ 计算效率: 与原版相近")
        print(f"   🏆 综合推荐: 平衡版FastALNS - 解质量最优，计算稳定")

def main():
    """主函数"""
    print("🚀 电动公交疏散ALNS算法性能对比")
    print("="*60)
    
    # 创建性能对比器
    comparator = PerformanceComparator()
    
    # 定义测试案例
    test_cases = [
        # {
        #     'name': 'Example案例',
        #     'depot_file': 'depot_information_example.xlsx',
        #     'route_file': 'route_information_example.xlsx',
        #     'passenger_file': 'passenger_demand_example.xlsx'
        # },
        {
            'name': 'Random案例',
            'depot_file': 'depot_information_random.xlsx',
            'route_file': 'route_information_random.xlsx',
            'passenger_file': 'passenger_demand_random.xlsx'
        }
    ]
    
    # 测试参数
    max_iterations = 100
    planning_horizon = 180.0
    return_buffer = 60.0
    num_runs = 1  # 可以设置多次运行取平均
    
    # 运行对比测试
    for test_case_config in test_cases:
        print(f"\n🔬 测试案例: {test_case_config['name']}")
        
        # 加载测试案例
        test_case = comparator.load_test_case(
            test_case_config['name'],
            test_case_config['depot_file'],
            test_case_config['route_file'],
            test_case_config['passenger_file']
        )
        
        if test_case is None:
            print(f"⏭️  跳过案例: {test_case_config['name']}")
            continue
        
        # 运行性能对比
        try:
            analysis = comparator.compare_algorithms(
                test_case, max_iterations, planning_horizon, return_buffer, num_runs
            )
            
            # 打印对比报告
            comparator.print_comparison_report(analysis, test_case_config['name'])
            
        except Exception as e:
            print(f"❌ 测试案例 {test_case_config['name']} 失败: {str(e)}")
            import traceback
            traceback.print_exc()
    
    print(f"\n🎯 性能对比测试完成!")

if __name__ == "__main__":
    main()