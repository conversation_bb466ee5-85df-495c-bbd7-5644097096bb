# 电动公交疏散调度基准测试说明

## 概述

`electric_bus_evacuation_bench.py` 是一个基准测试文件，用于评估在固定发车间隔条件下的电动公交疏散调度性能。与原始的ALNS优化算法不同，这个基准测试使用简化的调度策略。

## 主要特点

### 1. 固定发车间隔
- **发车间隔**: 20分钟（固定）
- **不进行发车时间优化**: 车辆按照固定间隔发车，不考虑乘客需求的动态变化

### 2. 车辆固定线路运行
- **线路分配**: 每辆车固定分配到一条线路
- **无线路间调度**: 车辆不会在不同线路间切换
- **返回起点**: 车辆到达线路终点后返回起点等待下次发车

### 3. 简化的车辆分配策略
- **平均分配**: 将所有车辆平均分配到各条线路
- **错开发车**: 同一线路的车辆错开发车时间，避免同时发车

## 测试结果示例

基于提供的测试数据，基准测试结果如下：

### 问题规模
- **车场数量**: 2个
- **线路数量**: 10条
- **车辆数量**: 45辆
- **乘客数量**: 23,192人
- **规划时间**: 180分钟（3小时）

### 运行结果
- **总运输人数**: 23,192人（100%疏散率）
- **使用车辆数**: 45辆（100%车辆利用率）
- **总行程数**: 150次
- **平均每车运输**: 515.4人
- **执行时间**: 2.67秒

### 车辆分配情况
- 线路0-4: 每条线路分配5辆车
- 线路5-9: 每条线路分配4辆车

## 与ALNS算法的对比

| 特性 | 基准测试 | ALNS算法 |
|------|----------|----------|
| 发车时间 | 固定20分钟间隔 | 动态优化 |
| 线路分配 | 固定分配 | 动态调度 |
| 车辆利用 | 平均分配 | 智能分配 |
| 计算复杂度 | 低 | 高 |
| 执行时间 | 快（秒级） | 慢（分钟级） |
| 解决方案质量 | 基准水平 | 优化水平 |

## 使用方法

### 运行基准测试
```bash
python electric_bus_evacuation_bench.py
```

### 输入文件要求
- `depot_information_random.xlsx`: 车场信息
- `route_information_random.xlsx`: 线路信息  
- `passenger_demand_random.xlsx`: 乘客需求数据

### 输出信息
1. **问题规模统计**: 车场、线路、车辆、乘客数量
2. **车辆分配情况**: 每条线路分配的车辆数量
3. **详细调度结果**: 每辆车的行程安排
4. **总结统计**: 运输人数、车辆利用率等关键指标

## 核心算法逻辑

### 1. 车辆分配
```python
# 平均分配车辆到各条线路
buses_per_route = len(buses) // len(routes)
remaining_buses = len(buses) % len(routes)
```

### 2. 发车调度
```python
# 固定20分钟发车间隔
departure_interval = 20.0  # 分钟
# 错开同线路车辆发车时间
initial_delay = bus_idx * (departure_interval / len(assigned_buses))
```

### 3. 充电管理
- 当车辆电量低于20%时自动返回车场充电
- 充电到80%后继续服务
- 考虑充电桩资源约束

## 应用价值

### 1. 性能基准
- 为ALNS等优化算法提供性能对比基准
- 评估优化算法的改进效果

### 2. 快速评估
- 快速评估不同规模问题的基本调度需求
- 为实际调度提供初步方案

### 3. 系统验证
- 验证数据输入格式和系统功能
- 测试不同参数设置的影响

## 局限性

1. **不考虑乘客到达时间分布**: 固定发车间隔可能导致早期或晚期乘客等待时间过长
2. **车辆利用效率不高**: 平均分配可能导致某些线路车辆闲置
3. **无动态调整能力**: 无法根据实时情况调整调度策略
4. **简化的充电策略**: 充电决策较为简单，可能不是最优

## 扩展建议

1. **增加发车间隔优化**: 根据乘客需求密度调整发车间隔
2. **改进车辆分配**: 基于线路长度和乘客需求进行智能分配
3. **添加性能指标**: 增加乘客等待时间、车辆空载率等指标
4. **支持多场景测试**: 支持不同规模和类型的测试场景
