#!/usr/bin/env python3
"""
测试Vehicle Usage Number显示是否正确
"""

import time
from solution_logger import SolutionLogger

def test_vehicle_usage_display():
    """测试Vehicle Usage Number显示"""
    print("🧪 测试Vehicle Usage Number显示...")
    
    # 测试原版ALNS
    from electric_bus_evacuation_alns import create_example_problem, ElectricBusEvacuationALNS
    
    # 创建问题数据（使用小数据集）
    buses, routes, depots, passengers = create_example_problem(
        depot_info_file="depot_information_example.xlsx",
        route_info_file="route_information_example.xlsx",
        passenger_data_file="passenger_demand_example.xlsx",
        bus_travel_speed=20.0
    )
    
    print(f"📊 问题规模: {len(buses)} 辆公交车, {len(routes)} 条路线, {len(passengers)} 名乘客")
    
    # 创建ALNS求解器
    alns = ElectricBusEvacuationALNS(
        buses=buses,
        routes=routes,
        depots=depots,
        passengers=passengers,
        planning_horizon=180.0,
        return_buffer=60.0
    )
    
    # 只生成初始解进行测试
    print("\n🔍 生成初始解并检查Vehicle Usage...")
    solution = alns.generate_initial_solution()
    
    # 手动计算使用的车辆数
    buses_used_manual = sum(1 for tour in solution.bus_tours if len(tour.trips) > 0)
    
    print(f"📊 解质量:")
    print(f"  • 乘客数: {solution.objective_value}")
    print(f"  • 总罚分: {solution.penalty_value}")
    print(f"  • 可行性: {solution.is_feasible}")
    print(f"  • 手动计算的车辆使用数: {buses_used_manual}")
    print(f"  • Vehicle Usage Penalty: {solution.vehicle_usage_penalty}")
    print(f"  • Vehicle Usage Number (penalty/50): {solution.vehicle_usage_penalty/50:.0f}")
    
    # 验证计算是否正确
    expected_penalty = buses_used_manual * 50
    if abs(solution.vehicle_usage_penalty - expected_penalty) < 0.1:
        print("✅ Vehicle Usage计算正确！")
    else:
        print(f"❌ Vehicle Usage计算错误: 期望{expected_penalty}, 实际{solution.vehicle_usage_penalty}")
    
    if solution.vehicle_usage_penalty > 0:
        print("✅ Vehicle Usage Number应该正常显示了！")
    else:
        print("⚠️  Vehicle Usage Penalty仍为0，可能存在其他问题")

if __name__ == "__main__":
    test_vehicle_usage_display()
