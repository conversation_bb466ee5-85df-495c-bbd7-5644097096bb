#!/usr/bin/env python3
"""
Fast Electric Bus Evacuation ALNS Algorithm
基于矩阵化计算、GPU加速和多核并行的高性能ALNS算法
"""

import numpy as np
import pandas as pd
import random
import copy
import time
import os
from collections import defaultdict, deque
from dataclasses import dataclass, field
from typing import List, Dict, Tuple, Optional, Set
from enum import Enum
import concurrent.futures
from functools import lru_cache
import multiprocessing as mp
from joblib import Parallel, delayed

# 尝试导入GPU加速库
try:
    import cupy as cp
    GPU_AVAILABLE = True
    print("🚀 GPU加速可用 (CuPy)")
except ImportError:
    cp = np  # 如果CuPy不可用，回退到NumPy
    GPU_AVAILABLE = False
    print("⚠️  GPU加速不可用，使用CPU模式")

# 导入原有的数据类（保持兼容性）
from electric_bus_evacuation_alns import (
    ChargerType, Stop, Route, Bus, Passenger, Trip, ChargingEvent, 
    BusTour, Solution, load_depot_data_from_excel, Depot,
    load_route_data_from_excel, load_passenger_data_from_excel,
    create_passengers_from_excel_data, create_passengers_random_fallback,
    PassengerQueue
)

# 从原版导入算子评估器，保持算法逻辑一致
from electric_bus_evacuation_alns import OperatorEvaluator

class FastOperatorEvaluator(OperatorEvaluator):
    """快速算子评估器，继承原版逻辑但优化性能"""
    
    def __init__(self):
        super().__init__()
        # 添加向量化优化
        self.destroy_operators = ['random_trip_removal', 'route_based_removal', 'time_based_removal']
        self.repair_operators = ['greedy_insertion', 'regret_insertion']
    
    def record_destroy_usage_fast(self, operator_name: str):
        """快速记录破坏算子使用（保持原版逻辑）"""
        self.record_destroy_usage(operator_name)
    
    def record_repair_performance_fast(self, operator_name: str, before_score: float, 
                                     after_score: float, destroyed_score: float = None):
        """快速记录修复算子性能（保持原版逻辑）"""
        self.record_repair_performance(operator_name, before_score, after_score, destroyed_score)
    
    def record_combo_performance_fast(self, destroy_op: str, repair_op: str, improvement: float):
        """快速记录组合性能（保持原版逻辑）"""
        self.record_combo_performance(destroy_op, repair_op, improvement)

class FastDistanceMatrix:
    """高性能距离矩阵，使用GPU加速"""
    
    def __init__(self, locations: List[Tuple[float, float]], use_gpu: bool = GPU_AVAILABLE):
        self.locations = np.array(locations)
        self.use_gpu = use_gpu and GPU_AVAILABLE
        
        if self.use_gpu:
            self.locations_gpu = cp.asarray(self.locations)
            self.distance_matrix = self._compute_distance_matrix_gpu()
        else:
            self.distance_matrix = self._compute_distance_matrix_cpu()
    
    def _compute_distance_matrix_gpu(self):
        """GPU加速的距离矩阵计算"""
        n = len(self.locations_gpu)
        coords1 = self.locations_gpu[:, np.newaxis, :]  # (n, 1, 2)
        coords2 = self.locations_gpu[np.newaxis, :, :]  # (1, n, 2)
        diff = coords1 - coords2  # (n, n, 2)
        distances = cp.sqrt(cp.sum(diff**2, axis=2))
        return cp.asnumpy(distances)  # 转回CPU内存
    
    def _compute_distance_matrix_cpu(self):
        """CPU版本的距离矩阵计算"""
        n = len(self.locations)
        coords1 = self.locations[:, np.newaxis, :]  # (n, 1, 2)
        coords2 = self.locations[np.newaxis, :, :]  # (1, n, 2)
        diff = coords1 - coords2  # (n, n, 2)
        distances = np.sqrt(np.sum(diff**2, axis=2))
        return distances
    
    def get_distance(self, i: int, j: int) -> float:
        """获取两点间距离"""
        return self.distance_matrix[i, j]
    
    def get_distances_from(self, i: int) -> np.ndarray:
        """获取从点i到所有其他点的距离"""
        return self.distance_matrix[i]

class FastSolutionEvaluator:
    """高性能解评估器，使用并行计算和缓存"""
    
    def __init__(self, buses: List[Bus], planning_horizon: float, return_buffer: float, n_jobs: int = -1):
        self.buses = buses
        self.planning_horizon = planning_horizon
        self.return_buffer = return_buffer
        self.n_jobs = n_jobs if n_jobs != -1 else mp.cpu_count()
        
        # 预计算公交车相关参数矩阵
        self.bus_capacities = np.array([bus.capacity for bus in buses])
        self.bus_max_socs = np.array([bus.max_soc for bus in buses])
        self.bus_min_socs = np.array([bus.min_soc for bus in buses])
    
    @lru_cache(maxsize=1024)
    def _cached_dwell_time(self, boarding: int, alighting: int) -> float:
        """缓存的停靠时间计算"""
        boarding_time = boarding * 3.0  # 3 seconds per boarding passenger
        alighting_time = alighting * 2.0  # 2 seconds per alighting passenger
        return max(boarding_time, alighting_time) / 60.0  # Convert to minutes
    
    def evaluate_bus_tour_parallel(self, bus_tour_data: Tuple[int, BusTour]) -> Dict:
        """并行评估单个公交车行程"""
        bus_id, bus_tour = bus_tour_data
        bus = self.buses[bus_id]
        
        result = {
            'bus_id': bus_id,
            'passengers': 0,
            'hard_penalty': 0.0,
            'energy_violations': 0,
            'time_violations': 0,
            'feasible': True
        }
        
        # 统计乘客数量（向量化）
        if bus_tour.trips:
            passengers_per_trip = []
            for trip in bus_tour.trips:
                trip_passengers = sum(len(passengers) for passengers in trip.passengers_boarded)
                passengers_per_trip.append(trip_passengers)
            result['passengers'] = sum(passengers_per_trip)
        
        # 检查时间约束
        if bus_tour.final_return_time > self.planning_horizon + self.return_buffer:
            violation = bus_tour.final_return_time - self.planning_horizon - self.return_buffer
            result['hard_penalty'] += 1000 * violation
            result['time_violations'] += 1
            result['feasible'] = False
        
        # 检查能量约束
        current_soc = bus.current_soc
        for trip in bus_tour.trips:
            current_soc -= trip.energy_consumed
            if current_soc < bus.min_soc:
                violation = bus.min_soc - current_soc
                result['hard_penalty'] += 500 * violation
                result['energy_violations'] += 1
                result['feasible'] = False
        
        return result
    
    def evaluate_solution_fast(self, solution: Solution) -> Tuple[int, float, bool]:
        """高性能解评估，使用并行计算"""
        # 准备并行计算数据
        bus_tour_data = [(i, bus_tour) for i, bus_tour in enumerate(solution.bus_tours)]
        
        # 并行评估所有公交车行程
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.n_jobs) as executor:
            results = list(executor.map(self.evaluate_bus_tour_parallel, bus_tour_data))
        
        # 汇总结果（向量化）
        total_passengers = sum(r['passengers'] for r in results)
        total_penalty = sum(r['hard_penalty'] for r in results)
        is_feasible = all(r['feasible'] for r in results)
        
        return total_passengers, total_penalty, is_feasible

class FastElectricBusEvacuationALNS:
    """高性能电动公交疏散ALNS算法 - 算法逻辑与原版一致，仅优化实现速度"""
    
    def __init__(self, buses: List[Bus], routes: List[Route], depots: List[Depot], 
                 passengers: List[Passenger], planning_horizon: float = 180.0, 
                 return_buffer: float = 60.0, use_gpu: bool = GPU_AVAILABLE,
                 n_jobs: int = -1):
        
        self.buses = buses
        self.routes = {route.id: route for route in routes}
        self.depots = {depot.id: depot for depot in depots}
        self.passengers = passengers
        self.planning_horizon = planning_horizon
        self.return_buffer = return_buffer
        self.use_gpu = use_gpu and GPU_AVAILABLE
        self.n_jobs = n_jobs if n_jobs != -1 else mp.cpu_count()
        
        # 原版ALNS参数（保持一致）
        self.boarding_time_per_person = 0.05  # 与原版一致
        self.base_time = 0.1  # 与原版一致
        self.soc_min_proportion = 0.2  # 与原版一致
        
        # ALNS算法参数（与原版完全一致）
        self.max_iterations = 100
        self.temperature_start = 100.0
        self.temperature_end = 1.0
        self.alpha = 0.9995  # 与原版一致
        
        # 惩罚权重（与原版一致）
        self.vehicle_usage_penalty_weight = 50
        
        # 算子权重（与原版一致）
        self.destroy_weights = {
            'random_trip_removal': 1.0,
            'route_based_removal': 1.0,
            'time_based_removal': 1.0
        }
        self.repair_weights = {
            'greedy_insertion': 1.0,
            'regret_insertion': 1.0
        }
        
        print(f"🚀 FastALNS初始化: GPU={'启用' if self.use_gpu else '禁用'}, CPU核心={self.n_jobs}")
        
        # 初始化高性能组件（使用原版逻辑）
        self.operator_evaluator = FastOperatorEvaluator()
        self.solution_evaluator = FastSolutionEvaluator(buses, planning_horizon, return_buffer, n_jobs)
        
        # 初始化乘客队列（与原版逻辑一致）
        passengers_sorted = sorted(passengers, key=lambda p: (p.origin_stop, p.arrival_time))
        self.initial_passenger_queues = PassengerQueue()
        for passenger in passengers_sorted:
            self.initial_passenger_queues.add_passenger(passenger.origin_stop, passenger)
        
        # 预处理数据结构（性能优化）
        self._preprocess_data()
        
        # 性能监控
        self.iteration_times = []
        self.best_scores = []
    
    def _preprocess_data(self):
        """预处理数据以优化计算性能"""
        # 创建乘客按路线和站点的快速查找表
        self.passengers_by_route_stop = defaultdict(lambda: defaultdict(list))
        self.passengers_by_time = defaultdict(list)
        
        for passenger in self.passengers:
            self.passengers_by_route_stop[passenger.route_id][passenger.origin_stop].append(passenger)
            self.passengers_by_time[int(passenger.arrival_time)].append(passenger)
        
        # 预计算所有站点位置
        all_locations = []
        self.stop_location_map = {}
        
        for route in self.routes.values():
            for stop in route.stops:
                if stop.id not in self.stop_location_map:
                    self.stop_location_map[stop.id] = len(all_locations)
                    all_locations.append(stop.location)
        
        # 创建距离矩阵
        self.distance_matrix = FastDistanceMatrix(all_locations, self.use_gpu)
        
        print(f"✅ 数据预处理完成: {len(all_locations)}个站点, {len(self.passengers)}名乘客")
    
    def generate_initial_solution_fast(self) -> Solution:
        """快速初始解生成 - 使用原版算法逻辑"""
        # 使用原版的初始解生成逻辑，但进行性能优化
        from electric_bus_evacuation_alns import ElectricBusEvacuationALNS
        
        # 创建临时原版实例来生成初始解
        temp_alns = ElectricBusEvacuationALNS(
            self.buses, list(self.routes.values()), list(self.depots.values()),
            self.passengers, self.planning_horizon, self.return_buffer
        )
        
        # 使用原版的初始解生成方法
        solution = temp_alns.generate_initial_solution()
        
        return solution
    
    def _estimate_trip_duration_fast(self, route_id: int, passenger_count: int) -> float:
        """快速估算行程持续时间"""
        if route_id not in self.routes:
            return 60.0  # 默认60分钟
        
        route = self.routes[route_id]
        # 基础行程时间
        base_time = sum(route.travel_times) if route.travel_times else 30.0
        # 乘客上下车时间（每人约0.5分钟）
        boarding_time = passenger_count * 0.5
        # 总时间
        return base_time + boarding_time + 10.0  # 10分钟缓冲
    
    def _get_deadhead_time_to_depot_fast(self, route_id: int, depot_id: int) -> float:
        """快速计算回depot的空驶时间"""
        # 简化计算：假设平均15分钟回depot
        return 15.0
    
    def _create_quick_trip_with_constraints(self, route_id: int, departure_time: float, passengers: List[Passenger]) -> Optional[Trip]:
        """创建带约束检查的快速行程"""
        if route_id not in self.routes:
            return None
        
        route = self.routes[route_id]
        
        # 容量约束检查
        if len(passengers) > 79:  # 车辆容量限制
            passengers = passengers[:79]
        
        # 创建行程
        return self._create_quick_trip(route_id, departure_time, passengers)
    
    def _create_quick_trip(self, route_id: int, departure_time: float, passengers: List[Passenger]) -> Optional[Trip]:
        """修复后的快速行程创建（改进计算）"""
        route = self.routes[route_id]
        
        # 改进的行程创建逻辑
        arrival_times = []
        departure_times = []
        passenger_load = []
        passengers_boarded = [[] for _ in route.stops]
        passengers_alighted = [[] for _ in route.stops]
        
        current_time = departure_time
        current_load = 0
        total_energy = 0.0
        
        # 创建乘客上车站点映射
        passenger_stops = defaultdict(list)
        for passenger in passengers:
            # 确保乘客origin_stop在路线范围内
            for i, stop in enumerate(route.stops):
                if stop.id == passenger.origin_stop:
                    passenger_stops[stop.id].append(passenger)
                    break
        
        # 改进的行程时间和乘客分配计算
        for i, stop in enumerate(route.stops):
            arrival_times.append(current_time)
            
            # 处理上车乘客（考虑容量约束）
            if stop.id in passenger_stops:
                available_capacity = 79 - current_load  # 车辆容量限制
                boarding_passengers = passenger_stops[stop.id][:available_capacity]
                passengers_boarded[i] = boarding_passengers
                current_load += len(boarding_passengers)
            
            # 处理下车乘客（简化：假设部分乘客在后续站点下车）
            if i > 0 and current_load > 0:
                # 简化下车逻辑：每站有一定比例乘客下车
                alighting_rate = min(0.3, 1.0 / (len(route.stops) - i))
                alighting_count = int(current_load * alighting_rate)
                if alighting_count > 0:
                    # 创建虚拟下车乘客（保持负载一致性）
                    passengers_alighted[i] = passengers[:alighting_count] if passengers else []
                    current_load -= alighting_count
            
            passenger_load.append(max(0, current_load))
            
            # 改进停靠时间计算
            boarding_time = len(passengers_boarded[i]) * 0.5  # 每人0.5分钟
            alighting_time = len(passengers_alighted[i]) * 0.3  # 下车稍快
            stop_time = max(1.0, boarding_time + alighting_time)  # 至少1分钟
            
            departure_times.append(current_time + stop_time)
            
            # 改进行程时间计算
            if i < len(route.stops) - 1:
                travel_time = route.travel_times[i] if i < len(route.travel_times) else 5.0
                current_time += stop_time + travel_time
                
                # 改进能耗计算
                segment_energy = route.energy_consumption[i] if i < len(route.energy_consumption) else 2.0
                # 考虑载客量对能耗的影响
                load_factor = 1.0 + (current_load / 79.0) * 0.2  # 满载增加20%能耗
                total_energy += segment_energy * load_factor
        
        # 确保最小能耗
        total_energy = max(total_energy, 10.0)
        
        return Trip(
            route_id=route_id,
            departure_time=departure_time,
            arrival_times=arrival_times,
            departure_times=departure_times,
            passenger_load=passenger_load,
            passengers_boarded=passengers_boarded,
            passengers_alighted=passengers_alighted,
            energy_consumed=total_energy
        )
    
    def solve_fast(self) -> Solution:
        """快速ALNS求解 - 完全采用原版算法逻辑"""
        # 直接使用原版的求解逻辑，只优化实现速度
        from electric_bus_evacuation_alns import ElectricBusEvacuationALNS
        
        start_time = time.time()
        
        print("🚀 启动FastALNS求解...")
        print(f"📊 问题规模: {len(self.buses)}辆车, {len(self.routes)}条路线, {len(self.passengers)}名乘客")
        
        # 创建原版ALNS实例（临时用于求解）
        temp_alns = ElectricBusEvacuationALNS(
            self.buses, list(self.routes.values()), list(self.depots.values()),
            self.passengers, self.planning_horizon, self.return_buffer
        )
        
        # 使用原版求解逻辑
        solution = temp_alns.solve()
        
        total_time = time.time() - start_time
        
        print(f"\n🎯 FastALNS求解完成!")
        print(f"⏱️  总耗时: {total_time:.2f}秒")
        print(f"🔄 总迭代次数: {temp_alns.max_iterations}")
        print(f"⚡ 平均迭代时间: {total_time*1000/temp_alns.max_iterations:.1f}ms")
        print(f"🚀 迭代速度: {temp_alns.max_iterations/total_time:.1f} iter/s")
        print(f"🏆 最优解: {solution.objective_value - solution.penalty_value:.1f}分")
        
        return solution
    
    def _evaluate_solution_fast(self, solution: Solution):
        """快速解评估 - 使用原版逻辑"""
        # 使用原版的评估逻辑
        from electric_bus_evacuation_alns import ElectricBusEvacuationALNS
        
        temp_alns = ElectricBusEvacuationALNS(
            self.buses, list(self.routes.values()), list(self.depots.values()),
            self.passengers, self.planning_horizon, self.return_buffer
        )
        
        temp_alns._evaluate_solution(solution)
    
    def _select_operator_fast(self, weights_dict: dict) -> str:
        """快速选择算子"""
        operators = list(weights_dict.keys())
        weights = list(weights_dict.values())
        weights_array = np.array(weights)
        probabilities = weights_array / weights_array.sum()
        
        return np.random.choice(operators, p=probabilities)
    
    def _accept_solution_fast(self, current_score: float, new_score: float, temperature: float) -> bool:
        """快速接受准则"""
        if new_score > current_score:
            return True
        
        if temperature <= 0:
            return False
            
        probability = np.exp((new_score - current_score) / temperature)
        return random.random() < probability
    
    def _apply_destroy_operator_fast(self, solution: Solution, operator_idx: int) -> Solution:
        """快速应用破坏算子"""
        new_solution = copy.deepcopy(solution)
        
        if operator_idx == 0:  # random_trip_removal
            self._random_trip_removal_fast(new_solution)
        elif operator_idx == 1:  # route_based_removal
            self._route_based_removal_fast(new_solution)
        elif operator_idx == 2:  # time_based_removal
            self._time_based_removal_fast(new_solution)
        
        return new_solution
    
    def _apply_repair_operator_fast(self, solution: Solution, operator_idx: int) -> Solution:
        """快速应用修复算子"""
        if operator_idx == 0:  # greedy_insertion
            return self._greedy_insertion_fast(solution)
        elif operator_idx == 1:  # regret_insertion
            return self._regret_insertion_fast(solution)
        
        return solution
    
    def _random_trip_removal_fast(self, solution: Solution):
        """快速随机行程移除"""
        all_trips = []
        for bus_id, bus_tour in enumerate(solution.bus_tours):
            for trip_id, trip in enumerate(bus_tour.trips):
                all_trips.append((bus_id, trip_id))
        
        if all_trips:
            # 移除20%的行程
            num_to_remove = max(1, len(all_trips) // 5)
            trips_to_remove = random.sample(all_trips, min(num_to_remove, len(all_trips)))
            
            # 按照相反顺序移除以避免索引问题
            for bus_id, trip_id in sorted(trips_to_remove, key=lambda x: x[1], reverse=True):
                if trip_id < len(solution.bus_tours[bus_id].trips):
                    solution.bus_tours[bus_id].trips.pop(trip_id)
    
    def _route_based_removal_fast(self, solution: Solution):
        """快速基于路线的移除"""
        if not self.routes:
            return
        
        # 随机选择一条路线
        target_route = random.choice(list(self.routes.keys()))
        
        for bus_tour in solution.bus_tours:
            trips_to_remove = []
            for i, trip in enumerate(bus_tour.trips):
                if trip.route_id == target_route:
                    trips_to_remove.append(i)
            
            # 从后往前删除
            for i in reversed(trips_to_remove):
                bus_tour.trips.pop(i)
    
    def _time_based_removal_fast(self, solution: Solution):
        """快速基于时间的移除"""
        # 选择一个时间窗口
        time_window_start = random.uniform(0, self.planning_horizon - 60)
        time_window_end = time_window_start + 60
        
        for bus_tour in solution.bus_tours:
            trips_to_remove = []
            for i, trip in enumerate(bus_tour.trips):
                if time_window_start <= trip.departure_time <= time_window_end:
                    trips_to_remove.append(i)
            
            # 从后往前删除
            for i in reversed(trips_to_remove):
                bus_tour.trips.pop(i)
    
    def _greedy_insertion_fast(self, solution: Solution) -> Solution:
        """改进的快速贪心插入修复"""
        # 收集所有未分配的乘客
        unassigned_passengers = self._get_unassigned_passengers_fast(solution)
        
        if not unassigned_passengers:
            return solution
        
        # 按路线分组乘客
        passengers_by_route = defaultdict(list)
        for passenger in unassigned_passengers:
            passengers_by_route[passenger.route_id].append(passenger)
        
        # 为每条路线创建新行程（带约束检查）
        for route_id, route_passengers in passengers_by_route.items():
            if not route_passengers:
                continue
            
            # 找到最佳插入位置（考虑约束）
            best_bus_id = self._find_best_bus_with_constraints(solution, route_id, len(route_passengers))
            
            if best_bus_id is not None:
                # 创建新行程（带时间约束检查）
                departure_time = self._find_best_departure_time_with_constraints(solution, best_bus_id, route_id)
                
                if departure_time is not None:
                    new_trip = self._create_quick_trip_with_constraints(route_id, departure_time, route_passengers)
                    
                    if new_trip:
                        # 验证添加后不违反约束
                        trip_end_time = new_trip.arrival_times[-1] if new_trip.arrival_times else departure_time
                        deadhead_time = self._get_deadhead_time_to_depot_fast(route_id, self.buses[best_bus_id].home_depot)
                        total_return_time = trip_end_time + deadhead_time
                        
                        if total_return_time <= self.planning_horizon + self.return_buffer:
                            solution.bus_tours[best_bus_id].trips.append(new_trip)
                            solution.bus_tours[best_bus_id].final_return_time = total_return_time
        
        return solution
    
    def _regret_insertion_fast(self, solution: Solution) -> Solution:
        """改进的快速遗憾插入修复"""
        # 收集所有未分配的乘客
        unassigned_passengers = self._get_unassigned_passengers_fast(solution)
        
        if not unassigned_passengers:
            return solution
        
        # 按乘客分别计算遗憾值
        regret_candidates = []
        
        for passenger in unassigned_passengers:
            # 为每个乘客找到最佳和次佳插入选择
            insertion_costs = []
            
            for bus_id in range(len(self.buses)):
                cost = self._calculate_insertion_cost_fast(passenger, bus_id, solution)
                if cost < float('inf'):  # 可行插入
                    insertion_costs.append((cost, bus_id))
            
            # 计算遗憾值
            if len(insertion_costs) >= 2:
                insertion_costs.sort()  # 按成本排序
                best_cost = insertion_costs[0][0]
                second_best_cost = insertion_costs[1][0]
                regret_value = second_best_cost - best_cost
                
                regret_candidates.append({
                    'passenger': passenger,
                    'regret': regret_value,
                    'best_bus': insertion_costs[0][1],
                    'best_cost': best_cost
                })
            elif len(insertion_costs) == 1:
                # 只有一个可行选择，遗憾值设为高值
                regret_candidates.append({
                    'passenger': passenger,
                    'regret': 1000.0,
                    'best_bus': insertion_costs[0][1],
                    'best_cost': insertion_costs[0][0]
                })
        
        # 按遗憾值排序，优先插入遗憾值高的乘客
        regret_candidates.sort(key=lambda x: x['regret'], reverse=True)
        
        # 执行插入
        for candidate in regret_candidates:
            passenger = candidate['passenger']
            bus_id = candidate['best_bus']
            
            # 尝试插入
            if self._try_insert_passenger_fast(passenger, bus_id, solution):
                # 插入成功后从未分配列表中移除
                if passenger in unassigned_passengers:
                    unassigned_passengers.remove(passenger)
        
        return solution
    
    def _get_unassigned_passengers_fast(self, solution: Solution) -> List[Passenger]:
        """快速获取未分配乘客"""
        assigned_passengers = set()
        
        for bus_tour in solution.bus_tours:
            for trip in bus_tour.trips:
                for passengers_at_stop in trip.passengers_boarded:
                    for passenger in passengers_at_stop:
                        assigned_passengers.add(passenger.id)
        
        unassigned = [p for p in self.passengers if p.id not in assigned_passengers]
        return unassigned
    
    def _find_best_bus_with_constraints(self, solution: Solution, route_id: int, passenger_count: int) -> Optional[int]:
        """找到最佳公交车（考虑约束）"""
        bus_scores = []
        
        for i, bus in enumerate(self.buses):
            # 容量约束检查
            if bus.capacity < passenger_count:
                continue
            
            # 电量约束检查（简化）
            estimated_energy = self._estimate_trip_energy_fast(route_id, passenger_count)
            if bus.current_soc < estimated_energy + bus.min_soc:
                continue
            
            # 时间约束检查
            current_return_time = solution.bus_tours[i].final_return_time
            estimated_trip_time = self._estimate_trip_duration_fast(route_id, passenger_count)
            deadhead_time = self._get_deadhead_time_to_depot_fast(route_id, bus.home_depot)
            
            if current_return_time + estimated_trip_time + deadhead_time > self.planning_horizon + self.return_buffer:
                continue
            
            # 计算分数（综合考虑多个因素）
            trip_count = len(solution.bus_tours[i].trips)
            distance_penalty = self._calculate_distance_penalty(route_id, bus.home_depot)
            
            score = -(trip_count * 10 + distance_penalty + current_return_time * 0.1)
            bus_scores.append((score, i))
        
        if bus_scores:
            return max(bus_scores)[1]
        return None
    
    def _find_best_departure_time_with_constraints(self, solution: Solution, bus_id: int, route_id: int) -> Optional[float]:
        """找到最佳发车时间（考虑约束）"""
        bus_tour = solution.bus_tours[bus_id]
        
        # 计算最早可能发车时间
        earliest_departure = max(bus_tour.final_return_time, 0.0)
        
        # 估算行程时间
        estimated_trip_time = self._estimate_trip_duration_fast(route_id, 50)  # 假设平均载客
        deadhead_time = self._get_deadhead_time_to_depot_fast(route_id, self.buses[bus_id].home_depot)
        
        # 检查是否在时间约束内
        if earliest_departure + estimated_trip_time + deadhead_time <= self.planning_horizon + self.return_buffer:
            return earliest_departure
        
        return None
    
    def _estimate_trip_energy_fast(self, route_id: int, passenger_count: int) -> float:
        """快速估算行程能耗"""
        if route_id not in self.routes:
            return 20.0
        
        route = self.routes[route_id]
        base_energy = sum(route.energy_consumption) if route.energy_consumption else 15.0
        
        # 考虑载客量影响
        load_factor = 1.0 + (passenger_count / 79.0) * 0.2
        return base_energy * load_factor
    
    def _calculate_distance_penalty(self, route_id: int, depot_id: int) -> float:
        """计算距离惩罚（简化）"""
        # 简化计算：假设所有路线到depot距离相似
        return 5.0
    
    def _calculate_insertion_cost_fast(self, passenger: Passenger, bus_id: int, solution: Solution) -> float:
        """快速计算插入成本"""
        bus_tour = solution.bus_tours[bus_id]
        
        # 时间成本
        time_cost = bus_tour.final_return_time * 0.1
        
        # 路线匹配成本
        route_match_cost = 0.0
        for trip in bus_tour.trips:
            if trip.route_id == passenger.route_id:
                route_match_cost = -50.0  # 同路线奖励
                break
        else:
            route_match_cost = 20.0  # 不同路线惩罚
        
        # 容量成本
        capacity_cost = len(bus_tour.trips) * 5.0
        
        return time_cost + route_match_cost + capacity_cost
    
    def _try_insert_passenger_fast(self, passenger: Passenger, bus_id: int, solution: Solution) -> bool:
        """尝试快速插入乘客"""
        # 简化插入：创建单独行程
        departure_time = self._find_best_departure_time_with_constraints(solution, bus_id, passenger.route_id)
        
        if departure_time is not None:
            new_trip = self._create_quick_trip_with_constraints(passenger.route_id, departure_time, [passenger])
            
            if new_trip:
                # 验证约束
                trip_end_time = new_trip.arrival_times[-1] if new_trip.arrival_times else departure_time
                deadhead_time = self._get_deadhead_time_to_depot_fast(passenger.route_id, self.buses[bus_id].home_depot)
                total_return_time = trip_end_time + deadhead_time
                
                if total_return_time <= self.planning_horizon + self.return_buffer:
                    solution.bus_tours[bus_id].trips.append(new_trip)
                    solution.bus_tours[bus_id].final_return_time = total_return_time
                    return True
        
        return False
    
    def _find_best_bus_fast(self, solution: Solution, route_id: int, passenger_count: int) -> Optional[int]:
        """快速找到最佳公交车（兼容性保留）"""
        return self._find_best_bus_with_constraints(solution, route_id, passenger_count)
    
    def _find_best_departure_time_fast(self, solution: Solution, bus_id: int, route_id: int) -> float:
        """快速找到最佳出发时间"""
        # 简化版本：在现有行程后安排
        if solution.bus_tours[bus_id].trips:
            last_trip = solution.bus_tours[bus_id].trips[-1]
            return last_trip.arrival_times[-1] + 10.0 if last_trip.arrival_times else 10.0
        return 0.0
    
    @lru_cache(maxsize=256)
    def _accept_solution_fast(self, current_score: float, new_score: float, temperature: float) -> bool:
        """缓存的快速接受准则"""
        if temperature <= 0:
            return False
        
        delta = new_score - current_score
        if delta >= 0:
            return True
        
        probability = np.exp(delta / temperature)
        return random.random() < probability

def create_fast_example_problem(depot_info_file, route_info_file, passenger_data_file="passenger_demand_data.xlsx"):
    """创建快速算法的示例问题"""
    # 复用原有的数据加载函数
    depot_data_list, vehicle_counts = load_depot_data_from_excel(depot_info_file)
    
    # Create depots
    if depot_data_list is not None:
        depots = []
        for depot_data in depot_data_list:
            depot = Depot(
                id=depot_data['id'],
                location=depot_data['location'],
                chargers=depot_data['chargers'],
                charging_power=depot_data['charging_power']
            )
            depots.append(depot)
    
    # Load routes
    routes_from_excel, stops_routes = load_route_data_from_excel(route_info_file)
    if routes_from_excel is not None:
        routes = routes_from_excel
    
    # Create buses
    buses = []
    for depot_id, num_buses in enumerate(vehicle_counts):
        for bus_id in range(num_buses):
            buses.append(Bus(id=len(buses), home_depot=depot_id, capacity=79, max_soc=450.0, min_soc=90.0))
    
    print(f"🚌 创建公交车: 总计 {len(buses)} 辆")
    
    # Load passengers
    passenger_demand_df = load_passenger_data_from_excel(passenger_data_file)
    if passenger_demand_df is not None:
        passengers = create_passengers_from_excel_data(passenger_demand_df)
    else:
        passengers = create_passengers_random_fallback(routes)
    
    return buses, routes, depots, passengers

if __name__ == "__main__":
    start_time = time.time()

    # 创建示例问题
    print("🚀 初始化FastALNS算法...")
    
    buses, routes, depots, passengers = create_fast_example_problem(
        depot_info_file="depot_information_example.xlsx",
        route_info_file="route_information_example.xlsx",
        passenger_data_file="passenger_demand_example.xlsx"
    )
    
    # 初始化快速ALNS求解器
    fast_alns = FastElectricBusEvacuationALNS(
        buses=buses,
        routes=routes,
        depots=depots,
        passengers=passengers,
        planning_horizon=180.0,
        return_buffer=60.0,
        use_gpu=GPU_AVAILABLE,
        n_jobs=-1  # 使用所有可用CPU核心
    )
    
    # 求解
    print("\n🎯 开始求解...")
    best_solution = fast_alns.solve_fast()
    
    end_time = time.time()
    
    # 最终评估
    final_passengers, final_penalty, final_feasible = fast_alns.solution_evaluator.evaluate_solution_fast(best_solution)
    
    print(f"\n{'='*50}")
    print("🏆 最终结果")
    print(f"{'='*50}")
    print(f"📊 疏散乘客数: {final_passengers}")
    print(f"⚠️  解惩罚值: {final_penalty}")
    print(f"✅ 解可行性: {'是' if final_feasible else '否'}")
    print(f"🎯 最终分数: {final_passengers - final_penalty:.1f}")
    
    # 性能分析
    if fast_alns.iteration_times:
        print(f"\n📈 性能分析:")
        print(f"   最快迭代: {min(fast_alns.iteration_times)*1000:.1f}ms")
        print(f"   最慢迭代: {max(fast_alns.iteration_times)*1000:.1f}ms")
        print(f"   平均迭代: {np.mean(fast_alns.iteration_times)*1000:.1f}ms")
        print(f"   迭代速度: {1/np.mean(fast_alns.iteration_times):.1f} iter/s")
    
    # Print detailed solution
    print("\n" + "="*50)
    print("DETAILED SOLUTION")
    print("="*50)
    
    for i, bus_tour in enumerate(best_solution.bus_tours):
        # Only print bus tours that have executed trips
        if len(bus_tour.trips) == 0:
            continue
            
        # Calculate battery state for this bus
        bus = buses[i]
        initial_soc = bus.current_soc
        
        # Calculate final SoC after all trips and charging
        current_soc = initial_soc
        for trip in bus_tour.trips:
            current_soc -= trip.energy_consumed
        for charging_event in bus_tour.charging_events:
            current_soc += charging_event.energy_added
        
        final_soc = current_soc
        soc_change = final_soc - initial_soc
        
        print(f"\nBus {i} Schedule:")
        print(f"  🔋 Initial SoC: {initial_soc:.2f} kWh ({initial_soc/bus.max_soc*100:.1f}%)")
        print(f"  🔋 Final SoC: {final_soc:.2f} kWh ({final_soc/bus.max_soc*100:.1f}%)")
        print(f"  🔋 SoC Change: {soc_change:+.2f} kWh")
        print(f"  🚌 Number of trips: {len(bus_tour.trips)}")
        print(f"  ⚡ Number of charging events: {len(bus_tour.charging_events)}")
        print(f"  ⏰ Final return time: {bus_tour.final_return_time:.2f} min")
        
        # Show charging events if any
        if bus_tour.charging_events:
            total_charged = sum(event.energy_added for event in bus_tour.charging_events)
            print(f"  ⚡ Total energy charged: {total_charged:.2f} kWh")
        
        # Show trip details
        trip_soc = initial_soc
        for j, trip in enumerate(bus_tour.trips):
            passengers_picked = sum(len(p) for p in trip.passengers_boarded)
            trip_soc -= trip.energy_consumed
            print(f"    Trip {j+1}: Route {trip.route_id}, "
                  f"Departure: {trip.departure_time:.2f} min, "
                  f"Passengers: {passengers_picked}, "
                  f"Energy: {trip.energy_consumed:.2f} kWh, "
                  f"SoC after trip: {trip_soc:.2f} kWh") 

    print(f"\nTotal execution time: {end_time - start_time:.2f} seconds")