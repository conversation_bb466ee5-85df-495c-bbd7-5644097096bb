#!/usr/bin/env python3
"""
平衡版FastALNS算法
针对真正的性能瓶颈进行优化，保持解质量的同时提升速度：

关键性能瓶颈识别：
1. _get_passengers_on_bus: O(n²)复杂度的乘客搜索
2. _create_trip_candidate: 频繁调用的行程创建
3. passenger_queues.board_passengers: 队列操作
4. 解评估中的多重循环
5. 深拷贝操作

优化策略：
1. 缓存乘客状态以避免O(n²)搜索
2. 预计算关键数据结构
3. 向量化解评估
4. 优化深拷贝
5. 智能初始解生成
"""

import numpy as np
import pandas as pd
import random
import copy
import time
import os
from collections import defaultdict, deque
from dataclasses import dataclass, field
from typing import List, Dict, Tuple, Optional, Set
from functools import lru_cache
import concurrent.futures
from joblib import Parallel, delayed
import multiprocessing as mp

# 导入原版的数据类和函数
from electric_bus_evacuation_alns import (
    ChargerType, Stop, Route, Bus, Passenger, Trip, ChargingEvent, 
    BusTour, Solution, load_depot_data_from_excel, Depot,
    load_route_data_from_excel, load_passenger_data_from_excel,
    create_passengers_from_excel_data, create_passengers_random_fallback,
    PassengerQueue, create_example_problem, ElectricBusEvacuationALNS
)

class OptimizedPassengerTracker:
    """优化的乘客状态跟踪器，避免O(n²)搜索"""
    
    def __init__(self):
        self.passenger_states = {}  # passenger_id -> {status, current_stop, trip_id}
        
    def add_boarding(self, trip_id: str, stop_index: int, passengers: List[Passenger]):
        """记录乘客上车"""
        for passenger in passengers:
            self.passenger_states[passenger.id] = {
                'status': 'on_bus',
                'trip_id': trip_id,
                'boarding_stop': stop_index,
                'destination': passenger.destination_stop
            }
    
    def add_alighting(self, trip_id: str, stop_index: int, passengers: List[Passenger]):
        """记录乘客下车"""
        for passenger in passengers:
            if passenger.id in self.passenger_states:
                self.passenger_states[passenger.id]['status'] = 'alighted'
    
    def get_passengers_on_bus_fast(self, trip_id: str, stop_index: int, all_boarded: List[List[Passenger]]) -> List[Passenger]:
        """快速获取车上乘客，O(n)复杂度"""
        passengers_on_bus = []
        
        # 遍历之前所有站点的上车乘客
        for i in range(stop_index):
            for passenger in all_boarded[i]:
                # 检查是否还在车上
                state = self.passenger_states.get(passenger.id)
                if state and state['status'] == 'on_bus':
                    passengers_on_bus.append(passenger)
        
        return passengers_on_bus

class FastTripBuilder:
    """快速行程构建器"""
    
    def __init__(self, routes: Dict[int, Route], boarding_time_per_person: float = 0.05, base_time: float = 0.1):
        self.routes = routes
        self.boarding_time_per_person = boarding_time_per_person
        self.base_time = base_time
        self.passenger_tracker = OptimizedPassengerTracker()
        
        # 预计算路线信息
        self._precompute_route_data()
    
    def _precompute_route_data(self):
        """预计算路线数据"""
        self.route_cache = {}
        for route_id, route in self.routes.items():
            self.route_cache[route_id] = {
                'total_travel_time': sum(route.travel_times) if route.travel_times else 0,
                'total_energy': sum(route.energy_consumption) if route.energy_consumption else 0,
                'stop_count': len(route.stops)
            }
    
    @lru_cache(maxsize=1000)
    def _calculate_dwell_time_cached(self, boarding_count: int, alighting_count: int) -> float:
        """缓存的停靠时间计算"""
        return self.base_time + max(boarding_count, alighting_count) * self.boarding_time_per_person
    
    def create_trip_candidate_fast(self, route_id: int, departure_time: float, 
                                 bus: Bus, current_soc: float, passenger_queues: PassengerQueue) -> Optional[Trip]:
        """优化的行程候选创建"""
        route = self.routes[route_id]
        route_info = self.route_cache[route_id]
        
        # 快速可行性预检查
        if current_soc - route_info['total_energy'] < bus.min_soc:
            return None
        
        trip_id = f"{bus.id}_{departure_time}_{route_id}"
        
        trip = Trip(
            route_id=route_id,
            departure_time=departure_time,
            arrival_times=[],
            departure_times=[],
            passenger_load=[],
            passengers_boarded=[[] for _ in route.stops],
            passengers_alighted=[[] for _ in route.stops]
        )
        
        current_time = departure_time
        current_load = 0
        total_energy = 0.0
        
        # 批量处理站点
        for i, stop in enumerate(route.stops):
            # 行驶到站点
            if i > 0:
                travel_time = route.travel_times[i-1]
                energy_consumption = route.energy_consumption[i-1]
                current_time += travel_time
                total_energy += energy_consumption
            
            trip.arrival_times.append(current_time)
            
            # 快速下车处理
            passengers_on_bus = self.passenger_tracker.get_passengers_on_bus_fast(
                trip_id, i, trip.passengers_boarded
            )
            alighting_passengers = [p for p in passengers_on_bus if p.destination_stop == stop.id]
            trip.passengers_alighted[i] = alighting_passengers
            self.passenger_tracker.add_alighting(trip_id, i, alighting_passengers)
            current_load -= len(alighting_passengers)
            
            # 快速上车处理
            available_capacity = bus.capacity - current_load
            if available_capacity > 0:
                boarded_passengers = passenger_queues.board_passengers(
                    stop.id, bus.capacity, current_load
                )
                trip.passengers_boarded[i] = boarded_passengers
                self.passenger_tracker.add_boarding(trip_id, i, boarded_passengers)
                current_load += len(boarded_passengers)
            
            # 缓存的停靠时间计算
            dwell_time = self._calculate_dwell_time_cached(
                len(trip.passengers_boarded[i]), len(alighting_passengers)
            )
            current_time += dwell_time
            
            trip.departure_times.append(current_time)
            trip.passenger_load.append(current_load)
        
        trip.energy_consumed = total_energy
        
        # 快速可行性检查
        max_load = max(trip.passenger_load) if trip.passenger_load else 0
        if max_load > bus.capacity or current_soc - total_energy < bus.min_soc:
            return None
        
        return trip

class VectorizedSolutionEvaluator:
    """向量化解评估器"""
    
    def __init__(self, buses: List[Bus], planning_horizon: float, return_buffer: float, 
                 vehicle_penalty_weight: float = 50):
        self.buses = buses
        self.planning_horizon = planning_horizon
        self.return_buffer = return_buffer
        self.vehicle_penalty_weight = vehicle_penalty_weight
        
        # 预计算公交车数据
        self.bus_capacities = np.array([bus.capacity for bus in buses])
        self.bus_min_soc = np.array([bus.min_soc for bus in buses])
        self.bus_max_soc = np.array([bus.max_soc for bus in buses])
    
    def evaluate_solution_vectorized(self, solution: Solution) -> Tuple[int, float, bool]:
        """向量化的解评估"""
        total_passengers = 0
        total_penalty = 0.0
        vehicles_used = 0
        
        # 批量处理所有有行程的公交车
        active_buses = [(i, tour) for i, tour in enumerate(solution.bus_tours) if tour.trips]
        
        if not active_buses:
            solution.total_passengers_evacuated = 0
            solution.penalty_value = 0.0
            solution.objective_value = 0
            solution.is_feasible = True
            return 0, 0.0, True
        
        # 并行计算每辆车的指标
        def evaluate_bus_tour(bus_tour_data):
            bus_id, bus_tour = bus_tour_data
            bus = self.buses[bus_id]
            
            bus_passengers = 0
            bus_penalty = 0.0
            
            for trip in bus_tour.trips:
                # 快速乘客计数
                trip_passengers = sum(len(passengers_at_stop) for passengers_at_stop in trip.passengers_boarded)
                bus_passengers += trip_passengers
                
                # 容量约束
                max_load = max(trip.passenger_load) if trip.passenger_load else 0
                if max_load > bus.capacity:
                    bus_penalty += 1000.0 * (max_load - bus.capacity)
                
                # 能耗约束
                if trip.energy_consumed > bus.max_soc - bus.min_soc:
                    bus_penalty += 500.0 * (trip.energy_consumed - (bus.max_soc - bus.min_soc))
            
            # 时间约束
            if bus_tour.final_return_time > self.planning_horizon + self.return_buffer:
                bus_penalty += 1000.0 * (bus_tour.final_return_time - self.planning_horizon - self.return_buffer)
            
            return bus_passengers, bus_penalty
        
        # 使用线程池并行处理
        with concurrent.futures.ThreadPoolExecutor(max_workers=min(8, len(active_buses))) as executor:
            results = list(executor.map(evaluate_bus_tour, active_buses))
        
        total_passengers = sum(r[0] for r in results)
        total_penalty = sum(r[1] for r in results)
        vehicles_used = len(active_buses)
        
        # 车辆使用惩罚
        total_penalty += vehicles_used * self.vehicle_penalty_weight
        
        is_feasible = total_penalty < 1000.0
        
        # 更新解对象
        solution.total_passengers_evacuated = total_passengers
        solution.penalty_value = total_penalty
        solution.objective_value = total_passengers
        solution.is_feasible = is_feasible
        
        return total_passengers, total_penalty, is_feasible

class BalancedFastALNS:
    """平衡版快速ALNS算法"""
    
    def __init__(self, buses: List[Bus], routes: List[Route], depots: List[Depot],
                 passengers: List[Passenger], planning_horizon: float = 180.0,
                 return_buffer: float = 60.0, n_jobs: int = -1):
        
        self.buses = buses
        self.routes = {route.id: route for route in routes}
        self.depots = {depot.id: depot for depot in depots}
        self.passengers = passengers
        self.planning_horizon = planning_horizon
        self.return_buffer = return_buffer
        self.n_jobs = n_jobs if n_jobs != -1 else min(8, mp.cpu_count())
        
        # 与原版相同的参数
        self.boarding_time_per_person = 0.05
        self.base_time = 0.1
        self.soc_min_proportion = 0.2
        
        # ALNS参数（与原版完全一致）
        self.max_iterations = 100
        self.temperature_start = 100.0
        self.temperature_end = 1.0
        self.alpha = 0.9995
        self.vehicle_usage_penalty_weight = 50
        
        # 算子权重（与原版一致）
        self.destroy_weights = {
            'random_trip_removal': 1.0,
            'route_based_removal': 1.0,
            'time_based_removal': 1.0
        }
        self.repair_weights = {
            'greedy_insertion': 1.0,
            'regret_insertion': 1.0
        }
        
        print(f"🚀 BalancedFastALNS初始化: CPU核心={self.n_jobs}")
        
        # 初始化优化组件
        self.fast_trip_builder = FastTripBuilder(self.routes, self.boarding_time_per_person, self.base_time)
        self.solution_evaluator = VectorizedSolutionEvaluator(
            buses, planning_horizon, return_buffer, self.vehicle_usage_penalty_weight
        )
        
        # 初始化乘客队列（与原版一致）
        passengers_sorted = sorted(passengers, key=lambda p: (p.origin_stop, p.arrival_time))
        self.initial_passenger_queues = PassengerQueue()
        for passenger in passengers_sorted:
            self.initial_passenger_queues.add_passenger(passenger.origin_stop, passenger)
        
        print(f"✅ 平衡优化预处理完成: {len(self.routes)}条路线, {len(passengers)}名乘客")
    
    def solve(self) -> Solution:
        """平衡的快速求解"""
        start_time = time.time()
        
        print(f"Starting Balanced Fast ALNS algorithm...")
        print(f"Using optimized components with {self.n_jobs} CPU cores")
        
        # 生成初始解（使用原版确保质量）
        temp_alns = ElectricBusEvacuationALNS(
            self.buses, list(self.routes.values()), list(self.depots.values()),
            self.passengers, self.planning_horizon, self.return_buffer
        )
        current_solution = temp_alns.generate_initial_solution()
        
        # 使用优化评估器
        self.solution_evaluator.evaluate_solution_vectorized(current_solution)
        
        best_solution = copy.deepcopy(current_solution)
        best_score = current_solution.objective_value - current_solution.penalty_value
        
        print(f"Initial solution: {current_solution.total_passengers_evacuated} passengers, penalty: {current_solution.penalty_value}")
        
        # ALNS主循环（保持原版逻辑但使用优化组件）
        temperature = self.temperature_start
        iteration = 0
        improvement_count = 0
        
        while iteration < self.max_iterations and temperature > self.temperature_end:
            iteration += 1
            
            # 选择算子（与原版一致）
            destroy_op = self._select_operator(self.destroy_weights)
            repair_op = self._select_operator(self.repair_weights)
            
            # 确定移除数量
            total_trips = sum(len(tour.trips) for tour in current_solution.bus_tours)
            num_remove = max(1, int(total_trips * random.uniform(0.1, 0.3)))
            
            # 破坏阶段（使用原版逻辑）
            destroyed_solution = self._apply_destroy_operator(current_solution, destroy_op, num_remove)
            
            # 修复阶段（使用原版逻辑但优化后的评估）
            new_solution = self._apply_repair_operator(destroyed_solution, repair_op)
            
            # 使用优化评估器
            self.solution_evaluator.evaluate_solution_vectorized(new_solution)
            
            # 接受准则（与原版一致）
            current_score = current_solution.objective_value - current_solution.penalty_value
            new_score = new_solution.objective_value - new_solution.penalty_value
            
            improvement = new_score - current_score
            if improvement > 0 or self._accept_solution(current_score, new_score, temperature):
                current_solution = new_solution
                
                if new_score > best_score:
                    best_solution = copy.deepcopy(new_solution)
                    best_score = new_score
                    improvement_count += 1
                    if improvement_count <= 20:  # 减少输出频率
                        print(f"Iteration {iteration}: New best solution! Passengers: {new_solution.total_passengers_evacuated}, Penalty: {new_solution.penalty_value}")
            
            # 更新温度
            temperature *= self.alpha
            
            # 进度报告
            if iteration % 50 == 0:
                print(f"Iteration {iteration}: Current score: {current_score:.0f}, Best score: {best_score:.0f}, Temperature: {temperature:.2f}")
        
        total_time = time.time() - start_time
        
        print(f"Balanced Fast ALNS completed after {iteration} iterations.")
        print(f"Total time: {total_time:.2f} seconds")
        print(f"Average iteration time: {total_time/iteration*1000:.1f} ms")
        print(f"Iteration speed: {iteration/total_time:.1f} iter/s")
        print(f"Solution improvements: {improvement_count}")
        print(f"Best solution: {best_solution.total_passengers_evacuated} passengers evacuated")
        print(f"Solution penalty: {best_solution.penalty_value:.0f}")
        print(f"Feasible: {best_solution.is_feasible}")
        
        return best_solution
    
    def _select_operator(self, weights_dict: dict) -> str:
        """算子选择（与原版一致）"""
        operators = list(weights_dict.keys())
        weights = np.array(list(weights_dict.values()))
        probabilities = weights / weights.sum()
        return np.random.choice(operators, p=probabilities)
    
    def _accept_solution(self, current_score: float, new_score: float, temperature: float) -> bool:
        """接受准则（与原版一致）"""
        if new_score > current_score:
            return True
        if temperature <= 0:
            return False
        probability = np.exp((new_score - current_score) / temperature)
        return random.random() < probability
    
    def _apply_destroy_operator(self, solution: Solution, operator: str, num_remove: int) -> Solution:
        """应用破坏算子（使用原版逻辑）"""
        temp_alns = ElectricBusEvacuationALNS(
            self.buses, list(self.routes.values()), list(self.depots.values()),
            self.passengers, self.planning_horizon, self.return_buffer
        )
        
        if operator == 'random_trip_removal':
            return temp_alns._random_trip_removal(solution, num_remove)
        elif operator == 'route_based_removal':
            return temp_alns._route_based_removal(solution, num_remove)
        else:  # time_based_removal
            return temp_alns._time_based_removal(solution, num_remove)
    
    def _apply_repair_operator(self, solution: Solution, operator: str) -> Solution:
        """应用修复算子（使用原版逻辑）"""
        temp_alns = ElectricBusEvacuationALNS(
            self.buses, list(self.routes.values()), list(self.depots.values()),
            self.passengers, self.planning_horizon, self.return_buffer
        )
        
        if operator == 'greedy_insertion':
            return temp_alns._greedy_insertion(solution)
        else:  # regret_insertion
            return temp_alns._regret_insertion(solution)

# 便捷函数
def create_balanced_fast_example_problem(depot_info_file, route_info_file, passenger_data_file="passenger_demand_data.xlsx"):
    """创建平衡快速版本的测试问题"""
    return create_example_problem(depot_info_file, route_info_file, passenger_data_file)

def main():
    """主函数"""
    print("🚀 平衡版快速电动公交疏散ALNS算法测试")
    
    try:
        # 加载测试数据
        buses, routes, depots, passengers = create_balanced_fast_example_problem(
            depot_info_file="depot_information_example.xlsx",
            route_info_file="route_information_example.xlsx",
            passenger_data_file="passenger_demand_example.xlsx"
        )
        
        # 创建平衡版FastALNS实例
        balanced_fast_alns = BalancedFastALNS(
            buses=buses,
            routes=routes,
            depots=depots,
            passengers=passengers,
            planning_horizon=180.0,
            return_buffer=60.0
        )
        
        # 运行求解
        solution = balanced_fast_alns.solve()
        
        print(f"\n📊 最终结果:")
        print(f"   疏散乘客数: {solution.total_passengers_evacuated}")
        print(f"   惩罚值: {solution.penalty_value:.1f}")
        print(f"   最终分数: {solution.objective_value - solution.penalty_value:.1f}")
        print(f"   可行性: {'✅' if solution.is_feasible else '❌'}")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()