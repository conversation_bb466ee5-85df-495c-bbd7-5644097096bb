#!/usr/bin/env python3
"""
测试增强版可行性检查功能
"""

import time
from solution_logger import SolutionLogger

def test_feasibility_improvements():
    """测试可行性检查的改进效果"""
    print("🧪 开始测试增强版可行性检查...")
    
    # 测试原版ALNS
    print("\n📊 测试原版ALNS (electric_bus_evacuation_alns.py)...")
    from electric_bus_evacuation_alns import create_example_problem, ElectricBusEvacuationALNS
    
    # 创建问题数据
    buses, routes, depots, passengers = create_example_problem(
        depot_info_file="depot_information_random.xlsx",
        route_info_file="route_information_random.xlsx",
        passenger_data_file="passenger_demand_random.xlsx",
        bus_travel_speed=20.0
    )
    
    # 创建日志记录器
    logger_alns = SolutionLogger("alns")
    logger_alns.set_problem_scale(
        depot_count=len(depots),
        route_count=len(routes),
        passenger_count=len(passengers),
        bus_count=len(buses)
    )
    
    with logger_alns.logging_context():
        # 测试原版ALNS
        start_time = time.time()
        alns = ElectricBusEvacuationALNS(
            buses=buses,
            routes=routes,
            depots=depots,
            passengers=passengers,
            planning_horizon=180.0,
            return_buffer=60.0,
            logger=logger_alns
        )
        
        # 只运行一次迭代来测试可行性
        alns.max_iterations = 5  # 减少迭代次数用于测试
        solution_alns = alns.solve()
        time_alns = time.time() - start_time
        
        logger_alns.log_final_summary(
            total_passengers=solution_alns.objective_value,
            total_penalty=solution_alns.penalty_value,
            is_feasible=solution_alns.is_feasible,
            execution_time=time_alns,
            buses_used=sum(1 for tour in solution_alns.bus_tours if len(tour.trips) > 0),
            total_trips=sum(len(tour.trips) for tour in solution_alns.bus_tours)
        )
    
    # 测试增强版ALNS
    print("\n📊 测试增强版ALNS (electric_bus_evacuation_alns_departure.py)...")
    from electric_bus_evacuation_alns_departure import create_example_problem as create_example_problem_dep, ElectricBusEvacuationALNS as ElectricBusEvacuationALNS_dep
    
    # 重新创建相同的问题数据
    buses_dep, routes_dep, depots_dep, passengers_dep = create_example_problem_dep(
        depot_info_file="depot_information_random.xlsx",
        route_info_file="route_information_random.xlsx",
        passenger_data_file="passenger_demand_random.xlsx",
        bus_travel_speed=20.0
    )
    
    # 创建日志记录器
    logger_dep = SolutionLogger("alns_departure")
    logger_dep.set_problem_scale(
        depot_count=len(depots_dep),
        route_count=len(routes_dep),
        passenger_count=len(passengers_dep),
        bus_count=len(buses_dep)
    )
    
    with logger_dep.logging_context():
        # 测试增强版ALNS
        start_time = time.time()
        alns_dep = ElectricBusEvacuationALNS_dep(
            buses=buses_dep,
            routes=routes_dep,
            depots=depots_dep,
            passengers=passengers_dep,
            planning_horizon=180.0,
            return_buffer=60.0,
            logger=logger_dep
        )
        
        alns_dep.max_iterations = 5  # 相同的迭代次数
        solution_dep = alns_dep.solve()
        time_dep = time.time() - start_time
        
        logger_dep.log_final_summary(
            total_passengers=solution_dep.objective_value,
            total_penalty=solution_dep.penalty_value,
            is_feasible=solution_dep.is_feasible,
            execution_time=time_dep,
            buses_used=sum(1 for tour in solution_dep.bus_tours if len(tour.trips) > 0),
            total_trips=sum(len(tour.trips) for tour in solution_dep.bus_tours)
        )
    
    # 比较结果
    print("\n📈 比较结果:")
    print(f"原版ALNS - 乘客数: {solution_alns.objective_value}, 罚分: {solution_alns.penalty_value:.1f}, 可行: {solution_alns.is_feasible}")
    print(f"增强版ALNS - 乘客数: {solution_dep.objective_value}, 罚分: {solution_dep.penalty_value:.1f}, 可行: {solution_dep.is_feasible}")
    
    # 可行性改进评估
    if solution_dep.is_feasible and not solution_alns.is_feasible:
        print("✅ 增强版可行性检查成功！从不可行变为可行")
    elif solution_dep.penalty_value < solution_alns.penalty_value:
        print("✅ 增强版可行性检查改进了解的质量！")
    elif solution_dep.is_feasible == solution_alns.is_feasible:
        print("➖ 两版本可行性相同，但增强版包含更严格的约束检查")
    else:
        print("⚠️  需要进一步调试增强版可行性检查")
    
    print("✅ 可行性测试完成！")

if __name__ == "__main__":
    test_feasibility_improvements()
