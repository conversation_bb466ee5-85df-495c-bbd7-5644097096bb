# 路线数据生成器使用说明

## 概述

这个项目新增了一个路线数据生成器 (`route_data_generator.py`)，可以生成包含各个站点信息的Excel文件，并且更新了主程序 (`electric_bus_evacuation_alns.py`) 以支持从Excel文件加载路线数据。

## 文件结构

### 新增文件
- `route_data_generator.py` - 路线数据生成器主程序
- `route_information_example.xlsx` - 示例路线数据文件
- `route_information_random.xlsx` - 随机生成的路线数据文件

### 更新文件
- `electric_bus_evacuation_alns.py` - 主程序，新增了 `load_route_data_from_excel()` 函数

## 路线数据格式

每个路线的Excel文件包含以下列信息：

| 列名 | 描述 | 数据范围/计算方式 |
|------|------|------------------|
| `route_id` | 路线编号 | 整数，从0开始 |
| `stop_id` | 站点ID（全局唯一） | 整数，确保不重复 |
| `position_on_route` | 站点在路线中的位置 | 从0开始的序号 |
| `x_coordinate` | X坐标 | 0-100范围内的随机数 |
| `y_coordinate` | Y坐标 | 0-100范围内的随机数 |
| `travel_distance` | 到下一站的距离(km) | 0.8-1.5km随机生成，终点站为0 |
| `energy_consumption` | 到下一站的能耗(kWh) | travel_distance × 1.75 kWh/km |

## 使用方法

### 1. 生成路线数据

运行路线数据生成器：

```bash
python route_data_generator.py
```

程序会提示选择生成模式：
- **选项1（随机生成）**：可自定义路线数量、每条路线的站点数范围、区域大小
- **选项2（示例数据）**：使用预设的3条示例路线

### 2. 生成器参数说明

随机生成模式可配置：
- `num_routes`: 路线数量（默认5条）
- `min_stops_per_route`: 每条路线最少站点数（默认5个）
- `max_stops_per_route`: 每条路线最多站点数（默认15个）
- `area_width`: 区域宽度（默认100.0）
- `area_height`: 区域高度（默认100.0）

### 3. 在主程序中使用

主程序 `electric_bus_evacuation_alns.py` 已更新为自动加载路线数据：

```python
# 自动从Excel文件加载路线数据
buses, routes, depots, passengers = create_example_problem(
    depot_info_file="depot_information_random.xlsx",
    route_info_file="route_information_example.xlsx"  # 新增的参数
)
```

## Excel文件结构

生成的Excel文件包含以下工作表：

1. **Route_0, Route_1, ...**：每条路线单独一个工作表
2. **All_Routes**：所有路线合并的数据表（程序主要读取这个表）
3. **Summary**：汇总统计信息

## 关键功能特性

### 1. 站点ID唯一性
- 全局站点ID确保不重复，避免冲突
- 每条路线的站点按 `position_on_route` 排序

### 2. 旅行时间计算
- 使用 `travel_distance` 和平均车速20km/h计算
- 公式：`travel_time = (travel_distance / 20.0) × 60.0` (分钟)

### 3. 能耗计算
- 基于距离的线性模型：1.75 kWh/km
- 公式：`energy_consumption = travel_distance × 1.75`

### 4. 兼容性设计
- 如果Excel文件不存在，自动回退到默认路线生成
- 保持向后兼容性

## 示例输出

运行成功后会看到类似输出：

```
✅ 成功从 route_information_example.xlsx 读取 15 个站点的路线信息
✅ 成功创建 3 条路线
  📍 Route 0: 5 站点, 总距离: 4.60 km, 总能耗: 8.05 kWh
  📍 Route 1: 6 站点, 总距离: 5.80 km, 总能耗: 10.15 kWh
  📍 Route 2: 4 站点, 总距离: 3.40 km, 总能耗: 5.95 kWh
✅ 使用从Excel文件加载的 3 条路线
```

## 注意事项

1. **坐标系统**：使用笛卡尔坐标系，范围0-100
2. **距离单位**：公里(km)
3. **能耗单位**：千瓦时(kWh)
4. **时间单位**：分钟(min)
5. **车速假设**：平均车速20km/h用于计算旅行时间

## 扩展性

路线数据生成器设计为易于扩展：
- 可以修改距离范围、能耗系数等参数
- 可以添加更多站点属性（如乘客密度、站点类型等）
- 支持不同的坐标系统和约束条件