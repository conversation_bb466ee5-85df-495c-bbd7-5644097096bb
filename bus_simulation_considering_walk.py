import numpy as np
import random
from collections import defaultdict, deque
import matplotlib.pyplot as plt

'''
generate_passenger_arrivals(): 生成乘客到达
adjust_passengers_for_skip_stop(): 调整跳站策略下的乘客分布
simulate_bus_trip(): 模拟单次公交行程
run_simulation(): 运行完整仿真
compare_strategies(): 对比两种策略效果
'''

class BusSimulation:
    def __init__(self):
        # 基本参数
        self.num_stations = 10
        self.high_arrival_stations = [2, 4, 6, 8, 10]  # 高到达率站点
        self.low_arrival_stations = [1, 3, 5, 7, 9]   # 低到达率站点
        self.high_arrival_rate = 10  # 人/分钟
        self.low_arrival_rate = 2    # 人/分钟
        self.bus_frequency = 10      # 分钟
        self.bus_capacity = 30       # 人
        self.simulation_time = 120   # 分钟
        self.travel_time_between_stations = 3  # 分钟
        self.base_stop_time = 1      # 分钟
        self.boarding_time_per_person = 0.1  # 分钟
        self.skip_stations = [4, 6, 8]  # 跳站策略中跳过的站点
        self.walking_time_to_adjacent = 6  # 步行到相邻站点的时间（分钟）
        
        # 仿真状态
        self.reset_simulation()
    
    def reset_simulation(self):
        """重置仿真状态"""
        self.passengers_at_stations = defaultdict(list)  # 每个站点的等待乘客
        self.total_passengers_served = 0
        self.total_waiting_time = 0
        self.total_walking_time = 0  # 新增：总步行时间
        self.buses = []  # 公交车列表
        
    def generate_passenger_arrivals(self, station, current_time, time_interval):
        """生成乘客到达"""
        if station in self.high_arrival_stations:
            arrival_rate = self.high_arrival_rate
        else:
            arrival_rate = self.low_arrival_rate
        
        # 泊松分布生成到达人数
        num_arrivals = np.random.poisson(arrival_rate * time_interval)
        
        for _ in range(num_arrivals):
            # 乘客目的地：当前站点+3站，但不超过站点10
            destination = min(station + 3, 10)
            passenger = {
                'arrival_time': current_time,
                'origin': station,
                'destination': destination,
                'waiting_time': 0,
                'walking_time': 0
            }
            self.passengers_at_stations[station].append(passenger)
    
    def adjust_passengers_for_skip_stop(self):
        """调整跳站策略下的乘客分布"""
        for skip_station in self.skip_stations:
            # 处理从跳站出发的乘客
            passengers_to_move = self.passengers_at_stations[skip_station].copy()
            self.passengers_at_stations[skip_station] = []
            
            for passenger in passengers_to_move:
                # 随机选择相邻站点
                adjacent_stations = []
                if skip_station > 1:
                    adjacent_stations.append(skip_station - 1)
                if skip_station < 10:
                    adjacent_stations.append(skip_station + 1)
                
                new_origin = random.choice(adjacent_stations)
                passenger['origin'] = new_origin
                passenger['walking_time'] = self.walking_time_to_adjacent  # 记录步行时间
                self.total_walking_time += self.walking_time_to_adjacent
                self.passengers_at_stations[new_origin].append(passenger)
            
            # 处理到达跳站的乘客（调整其他站点乘客的目的地）
            for station in range(1, 11):
                if station != skip_station:
                    passengers = self.passengers_at_stations[station]
                    for passenger in passengers:
                        if passenger['destination'] == skip_station:
                            # 随机选择相邻站点作为新目的地
                            adjacent_stations = []
                            if skip_station > 1:
                                adjacent_stations.append(skip_station - 1)
                            if skip_station < 10:
                                adjacent_stations.append(skip_station + 1)
                            
                            passenger['destination'] = random.choice(adjacent_stations)
                            passenger['walking_time'] = self.walking_time_to_adjacent  # 记录步行时间
                            self.total_walking_time += self.walking_time_to_adjacent
    
    def calculate_stop_time(self, boarding_count, alighting_count):
        """计算停站时间"""
        return self.base_stop_time + max(boarding_count * self.boarding_time_per_person,
                                       alighting_count * self.boarding_time_per_person)
    
    def simulate_bus_trip(self, bus_id, departure_time, use_skip_stop=False):
        """模拟单次公交行程"""
        current_time = departure_time
        passengers_on_bus = []
        total_trip_time = 0
        
        stations_to_visit = list(range(1, 11))
        if use_skip_stop:
            stations_to_visit = [s for s in stations_to_visit if s not in self.skip_stations]
        
        for i, station in enumerate(stations_to_visit):
            # 到达站点时间
            if i > 0:
                current_time += self.travel_time_between_stations
            
            # 下车乘客
            alighting_passengers = [p for p in passengers_on_bus if p['destination'] == station]
            passengers_on_bus = [p for p in passengers_on_bus if p['destination'] != station]
            alighting_count = len(alighting_passengers)
            
            # 上车乘客
            waiting_passengers = self.passengers_at_stations[station]
            boarding_passengers = []
            
            for passenger in waiting_passengers[:]:
                if len(passengers_on_bus) + len(boarding_passengers) < self.bus_capacity:
                    passenger['waiting_time'] = current_time - passenger['arrival_time']
                    boarding_passengers.append(passenger)
                    waiting_passengers.remove(passenger)
            
            passengers_on_bus.extend(boarding_passengers)
            boarding_count = len(boarding_passengers)
            
            # 计算停站时间
            if boarding_count > 0 or alighting_count > 0:
                stop_time = self.calculate_stop_time(boarding_count, alighting_count)
                current_time += stop_time
                total_trip_time += stop_time
            
            # 更新统计
            self.total_passengers_served += len(alighting_passengers)
            for passenger in boarding_passengers:
                self.total_waiting_time += passenger['waiting_time']
        
        # 终点站所有乘客下车
        self.total_passengers_served += len(passengers_on_bus)
        
        return current_time - departure_time
    
    def run_simulation(self, use_skip_stop=False):
        """运行完整仿真"""
        self.reset_simulation()
        
        # 生成整个仿真期间的乘客到达
        for minute in range(self.simulation_time):
            for station in range(1, 11):
                self.generate_passenger_arrivals(station, minute, 1)
        
        # 如果使用跳站策略，调整乘客分布
        if use_skip_stop:
            self.adjust_passengers_for_skip_stop()
        
        # 发车并模拟行程
        num_buses = self.simulation_time // self.bus_frequency
        for bus_id in range(num_buses):
            departure_time = bus_id * self.bus_frequency
            self.simulate_bus_trip(bus_id, departure_time, use_skip_stop)
        
        # 计算平均等待时间和步行时间
        avg_waiting_time = self.total_waiting_time / max(self.total_passengers_served, 1)
        avg_walking_time = self.total_walking_time / max(self.total_passengers_served, 1) if use_skip_stop else 0
        
        return {
            'total_passengers': self.total_passengers_served,
            'avg_waiting_time': avg_waiting_time,
            'total_waiting_time': self.total_waiting_time,
            'avg_walking_time': avg_walking_time,
            'total_walking_time': self.total_walking_time
        }

def compare_strategies():
    """对比两种策略的效果"""
    sim = BusSimulation()
    
    print("=== 公交线路Skip-Stop策略对比分析（考虑步行时间）===\n")
    
    # 运行多次仿真取平均值
    num_runs = 10
    
    # 原始策略结果
    original_results = []
    for _ in range(num_runs):
        result = sim.run_simulation(use_skip_stop=False)
        original_results.append(result)
    
    # Skip-stop策略结果
    skip_stop_results = []
    for _ in range(num_runs):
        result = sim.run_simulation(use_skip_stop=True)
        skip_stop_results.append(result)
    
    # 计算平均值
    orig_avg_passengers = np.mean([r['total_passengers'] for r in original_results])
    orig_avg_waiting = np.mean([r['avg_waiting_time'] for r in original_results])
    
    skip_avg_passengers = np.mean([r['total_passengers'] for r in skip_stop_results])
    skip_avg_waiting = np.mean([r['avg_waiting_time'] for r in skip_stop_results])
    walking_time_by_skip = np.mean([r['avg_walking_time'] for r in skip_stop_results])
    
    # 输出结果
    print("原始策略（所有站点停车）:")
    print(f"  运载乘客总数: {orig_avg_passengers:.1f} 人")
    print(f"  平均等待时间: {orig_avg_waiting:.2f} 分钟")
    
    print("\nSkip-Stop策略（跳过站点4,6,8）:")
    print(f"  运载乘客总数: {skip_avg_passengers:.1f} 人")
    print(f"  平均等待时间: {skip_avg_waiting:.2f} 分钟")
    print(f"  平均步行时间: {walking_time_by_skip:.2f} 分钟")
    
    print("\n策略对比:")
    passenger_improvement = ((skip_avg_passengers - orig_avg_passengers) / orig_avg_passengers) * 100
    waiting_improvement = ((skip_avg_waiting + walking_time_by_skip - orig_avg_waiting) / orig_avg_waiting) * 100
    
    print(f"  乘客运载量变化: {passenger_improvement:+.1f}%")
    print(f"  等待时间变化（含步行时间）: {waiting_improvement:+.1f}%")
    
    return {
        'original': {'passengers': orig_avg_passengers, 'waiting': orig_avg_waiting},
        'skip_stop': {'passengers': skip_avg_passengers, 'waiting': skip_avg_waiting, 'walking': walking_time_by_skip}
    }

if __name__ == "__main__":
    results = compare_strategies()