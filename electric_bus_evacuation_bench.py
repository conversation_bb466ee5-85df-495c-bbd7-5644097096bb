import numpy as np
import pandas as pd
import random
import copy
import time
import os
from collections import defaultdict, deque
from dataclasses import dataclass, field
from typing import List, Dict, Tuple, Optional
from enum import Enum

# Import necessary classes from the original file
from electric_bus_evacuation_alns_departure import (
    ChargerType, Depot, Stop, Route, Bus, Passenger, Trip, ChargingEvent, BusTour,
    PassengerQueue, Solution, ChargerResourceManager,
    load_depot_data_from_excel, load_route_data_from_excel, load_passenger_data_from_excel,
    create_passengers_from_excel_data, create_passengers_random_fallback
)

class ElectricBusEvacuationBench:
    """
    基准测试类：使用固定发车间隔（20分钟）的电动公交疏散调度
    - 不优化发车时间
    - 每辆车固定在一条线路上运行
    - 车辆到达终点后返回起点等待下次发车
    """
    
    def __init__(self, buses: List[Bus], routes: List[Route], depots: List[Depot],
                 passengers: List[Passenger], planning_horizon: float):
        self.buses = buses
        self.routes = {route.id: route for route in routes}
        self.depots = depots
        self.passengers = passengers
        self.planning_horizon = planning_horizon
        
        # 固定参数
        self.departure_interval = 20.0  # 固定发车间隔：20分钟
        self.boarding_time_per_person = 0.05  # 每人上下车时间（分钟）
        self.base_time = 0.1  # 基础停站时间（分钟）
        self.soc_min_proportion = 0.2  # 最低电量比例
        self.bus_travel_speed = 20  # km/h
        
        # 初始化充电资源管理器
        self.charger_manager = ChargerResourceManager(depots)
        
        # 初始化乘客队列
        self.initial_passenger_queues = PassengerQueue()
        passengers_sorted = sorted(passengers, key=lambda p: (p.origin_stop, p.arrival_time))
        for passenger in passengers_sorted:
            self.initial_passenger_queues.add_passenger(passenger.origin_stop, passenger)
    
    def solve(self) -> Solution:
        """
        求解基准问题：
        1. 为每条线路分配车辆
        2. 按固定间隔发车
        3. 计算总运输人数和所需车辆数
        """
        print("开始基准测试求解...")
        print(f"使用固定发车间隔: {self.departure_interval} 分钟")
        
        solution = Solution(self.buses)
        solution.passenger_queues = copy.deepcopy(self.initial_passenger_queues)
        
        # 为每条线路分配车辆并安排发车
        route_assignments = self._assign_buses_to_routes()
        
        for route_id, assigned_buses in route_assignments.items():
            print(f"线路 {route_id}: 分配 {len(assigned_buses)} 辆车")
            self._schedule_route_services_corrected(solution, route_id, assigned_buses)
        
        # 评估解决方案
        self._evaluate_solution(solution)
        
        return solution
    
    def _assign_buses_to_routes(self) -> Dict[int, List[Bus]]:
        """为每条线路分配车辆"""
        route_assignments = {route_id: [] for route_id in self.routes.keys()}
        
        # 简单分配策略：平均分配车辆到各条线路
        buses_per_route = len(self.buses) // len(self.routes)
        remaining_buses = len(self.buses) % len(self.routes)
        
        bus_index = 0
        for i, route_id in enumerate(self.routes.keys()):
            # 基础分配
            num_buses = buses_per_route
            # 剩余车辆分配给前几条线路
            if i < remaining_buses:
                num_buses += 1
            
            for _ in range(num_buses):
                if bus_index < len(self.buses):
                    route_assignments[route_id].append(self.buses[bus_index])
                    bus_index += 1
        
        return route_assignments
    
    def _schedule_route_services_corrected(self, solution: Solution, route_id: int, assigned_buses: List[Bus]):
        """为指定线路安排车辆服务（修正版）"""
        if not assigned_buses:
            return
        
        route = self.routes[route_id]
        
        # 计算单程行驶时间和返回时间
        single_trip_time = self._calculate_route_duration(route)
        return_to_start_time = self._get_return_to_start_time(route_id)
        
        # 为每辆车安排发车时刻表
        for bus_idx, bus in enumerate(assigned_buses):
            bus_tour = solution.bus_tours[bus.id]
            
            # 错开发车时间，避免所有车同时发车
            initial_delay = bus_idx * (self.departure_interval / len(assigned_buses))
            current_time = initial_delay
            current_soc = bus.current_soc
            
            # 持续发车直到规划时间结束
            while current_time + single_trip_time <= self.planning_horizon:
                # 检查是否需要充电
                if current_soc < bus.max_soc * self.soc_min_proportion:
                    charging_event = self._plan_charging(bus, current_soc, current_time)
                    if charging_event:
                        bus_tour.charging_events.append(charging_event)
                        current_soc += charging_event.energy_added
                        current_time = charging_event.end_time
                        continue
                    else:
                        break  # 无法充电，停止服务
                
                # 创建行程
                trip = self._create_fixed_trip(route_id, current_time, bus, solution.passenger_queues)
                if trip:
                    bus_tour.trips.append(trip)
                    current_soc -= trip.energy_consumed
                    
                    # 修正：考虑实际的周转时间
                    trip_end_time = trip.departure_times[-1]
                    actual_return_time = return_to_start_time
                    
                    # 计算下一次发车时间
                    earliest_next_departure = trip_end_time + actual_return_time
                    scheduled_next_departure = current_time + self.departure_interval
                    
                    # 选择更晚的时间作为下次发车时间
                    current_time = max(earliest_next_departure, scheduled_next_departure)
                else:
                    break
            
            # 设置最终返回时间
            if bus_tour.trips:
                last_trip = bus_tour.trips[-1]
                return_time = self._get_deadhead_time_to_depot(route_id, bus.home_depot)
                bus_tour.final_return_time = last_trip.departure_times[-1] + return_time
            else:
                bus_tour.final_return_time = current_time

    def _calculate_route_duration(self, route: Route) -> float:
        """计算线路单程行驶时间"""
        total_time = 0.0

        # 行驶时间
        for travel_time in route.travel_times:
            total_time += travel_time

        # 停站时间（假设每站平均停留时间）
        avg_dwell_time = self.base_time + 2 * self.boarding_time_per_person  # 假设平均2人上下车
        total_time += len(route.stops) * avg_dwell_time

        return total_time

    def _create_fixed_trip(self, route_id: int, departure_time: float,
                          bus: Bus, passenger_queues: PassengerQueue) -> Optional[Trip]:
        """创建固定发车时间的行程"""
        route = self.routes[route_id]

        # 从车场到起点的时间
        depot_to_start_time = self._get_depot_to_start_time(bus.home_depot, route_id)
        actual_departure_time = departure_time + depot_to_start_time

        trip = Trip(
            route_id=route_id,
            departure_time=actual_departure_time,
            arrival_times=[],
            departure_times=[],
            passenger_load=[],
            passengers_boarded=[[] for _ in route.stops],
            passengers_alighted=[[] for _ in route.stops]
        )

        current_time = actual_departure_time
        current_load = 0
        total_energy = depot_to_start_time * 1.75 / 3  # 到起点的能耗

        # 遍历每个站点
        for i, stop in enumerate(route.stops):
            if i > 0:
                travel_time = route.travel_times[i-1]
                energy_consumption = route.energy_consumption[i-1]
                current_time += travel_time
                total_energy += energy_consumption

            trip.arrival_times.append(current_time)

            # 乘客下车
            alighting_passengers = [p for p in self._get_passengers_on_bus(trip, i)
                                  if p.destination_stop == stop.id]
            trip.passengers_alighted[i] = alighting_passengers
            current_load -= len(alighting_passengers)

            # 乘客上车
            boarded_passengers = passenger_queues.board_passengers(
                stop.id, bus.capacity, current_load
            )
            trip.passengers_boarded[i] = boarded_passengers
            current_load += len(boarded_passengers)

            # 计算停站时间
            dwell_time = self._calculate_dwell_time(
                len(boarded_passengers), len(alighting_passengers)
            )
            current_time += dwell_time

            trip.departure_times.append(current_time)
            trip.passenger_load.append(current_load)

        trip.energy_consumed = total_energy
        return trip

    def _get_passengers_on_bus(self, trip: Trip, stop_index: int) -> List[Passenger]:
        """获取当前在车上的乘客列表"""
        passengers_on_bus = []

        for i in range(stop_index):
            for passenger in trip.passengers_boarded[i]:
                alighted = False
                for j in range(i+1, stop_index+1):
                    if passenger in trip.passengers_alighted[j]:
                        alighted = True
                        break
                if not alighted:
                    passengers_on_bus.append(passenger)

        return passengers_on_bus

    def _calculate_dwell_time(self, boarding_count: int, alighting_count: int) -> float:
        """计算停站时间"""
        return self.base_time + max(boarding_count, alighting_count) * self.boarding_time_per_person

    def _get_depot_to_start_time(self, depot_id: int, route_id: int) -> float:
        """计算从车场到路线起点的时间"""
        try:
            depot = next(d for d in self.depots if d.id == depot_id)
            route = self.routes[route_id]

            if not route.stops:
                return 10.0

            depot_location = depot.location
            first_stop_location = route.stops[0].location

            distance = self._calculate_euclidean_distance(depot_location, first_stop_location)
            travel_time = distance / 4  # 使用相同的速度计算

            return max(5.0, travel_time)
        except Exception:
            return 10.0

    def _get_return_to_start_time(self, route_id: int) -> float:
        """计算从路线终点返回起点的时间"""
        try:
            route = self.routes[route_id]
            if not route.stops or len(route.stops) < 2:
                return 5.0  # 默认最小时间
            
            last_stop_location = route.stops[-1].location
            first_stop_location = route.stops[0].location
            
            distance = self._calculate_euclidean_distance(last_stop_location, first_stop_location)
            return_time = distance / 4  # 使用相同的速度计算
            
            return max(5.0, return_time)
        except Exception:
            return 10.0  # 默认返回时间

    def _get_deadhead_time_to_depot(self, route_id: int, depot_id: int) -> float:
        """计算从路线终点到车场的时间"""
        try:
            route = self.routes[route_id]
            if not route.stops:
                return 10.0

            last_stop_location = route.stops[-1].location
            depot = next(d for d in self.depots if d.id == depot_id)
            depot_location = depot.location

            distance = self._calculate_euclidean_distance(last_stop_location, depot_location)
            deadhead_time = distance / 4

            return max(5.0, deadhead_time)
        except Exception:
            return 10.0

    def _calculate_euclidean_distance(self, point1: Tuple[float, float], point2: Tuple[float, float]) -> float:
        """计算欧几里得距离"""
        import math
        x1, y1 = point1
        x2, y2 = point2
        return math.sqrt((x2 - x1)**2 + (y2 - y1)**2)

    def _plan_charging(self, bus: Bus, current_soc: float, start_time: float) -> Optional[ChargingEvent]:
        """规划充电事件"""
        depot = next(d for d in self.depots if d.id == bus.home_depot)

        # 计算充电需求
        target_soc = bus.max_soc * 0.8  # 充电到80%
        energy_needed = target_soc - current_soc

        # 尝试不同类型的充电桩
        for charger_type in [ChargerType.TYPE_C, ChargerType.TYPE_B, ChargerType.TYPE_A]:
            if charger_type in depot.chargers and depot.chargers[charger_type] > 0:
                charging_power = depot.charging_power[charger_type]
                charging_time = energy_needed / charging_power * 60  # 转换为分钟

                end_time = start_time + charging_time

                if self.charger_manager.is_charger_available(depot.id, charger_type, start_time, end_time):
                    if self.charger_manager.reserve_charger(depot.id, charger_type, start_time, end_time):
                        return ChargingEvent(
                            depot_id=depot.id,
                            charger_type=charger_type,
                            start_time=start_time,
                            end_time=end_time,
                            energy_added=energy_needed
                        )

        return None

    def _evaluate_solution(self, solution: Solution):
        """评估解决方案"""
        total_passengers = 0
        buses_used = 0

        for bus_tour in solution.bus_tours:
            # 统计运输的乘客数量
            for trip in bus_tour.trips:
                for passengers_at_stop in trip.passengers_boarded:
                    total_passengers += len(passengers_at_stop)

            # 统计使用的车辆数量
            if len(bus_tour.trips) > 0:
                buses_used += 1

        solution.total_passengers_evacuated = total_passengers
        solution.objective_value = total_passengers
        solution.penalty_value = 0.0  # 基准测试不考虑惩罚
        solution.is_feasible = True

        print(f"基准测试结果:")
        print(f"  总运输人数: {total_passengers}")
        print(f"  使用车辆数: {buses_used}")
        print(f"  车辆利用率: {buses_used}/{len(self.buses)} ({buses_used/len(self.buses)*100:.1f}%)")

    def print_detailed_results(self, solution: Solution):
        """打印详细结果"""
        print("\n" + "="*60)
        print("基准测试详细结果")
        print("="*60)

        buses_used = 0
        total_trips = 0

        for bus_tour in solution.bus_tours:
            if len(bus_tour.trips) == 0:
                continue

            buses_used += 1
            total_trips += len(bus_tour.trips)

            bus = self.buses[bus_tour.bus_id]
            print(f"\n🚌 车辆 {bus_tour.bus_id} (车场 {bus.home_depot}):")
            print(f"  执行行程数: {len(bus_tour.trips)}")
            print(f"  充电次数: {len(bus_tour.charging_events)}")
            print(f"  最终返回时间: {bus_tour.final_return_time:.1f} 分钟")

            # 统计每个行程的乘客数
            for i, trip in enumerate(bus_tour.trips):
                passengers_count = sum(len(p) for p in trip.passengers_boarded)
                max_load = max(trip.passenger_load) if trip.passenger_load else 0
                print(f"    行程 {i+1}: 线路 {trip.route_id}, 出发 {trip.departure_time:.1f}分钟, 乘客 {passengers_count}人, 最大载客 {max_load}")

        print(f"\n📊 总结:")
        print(f"  使用车辆: {buses_used}/{len(self.buses)} 辆")
        print(f"  总行程数: {total_trips}")
        print(f"  总运输人数: {solution.total_passengers_evacuated}")
        print(f"  平均每车运输: {solution.total_passengers_evacuated/buses_used:.1f} 人" if buses_used > 0 else "  平均每车运输: 0 人")

    def print_trip_passenger_dynamics(self, solution: Solution, bus_id: int = 0, trip_index: int = 0):
        """
        打印指定行程的乘客上下车动态变化
        
        Args:
            solution: 求解方案
            bus_id: 车辆ID
            trip_index: 行程索引（该车辆的第几个行程）
        """
        if bus_id >= len(solution.bus_tours):
            print(f"❌ 车辆ID {bus_id} 不存在")
            return
        
        bus_tour = solution.bus_tours[bus_id]
        
        if trip_index >= len(bus_tour.trips):
            print(f"❌ 车辆 {bus_id} 的行程 {trip_index} 不存在")
            return
        
        trip = bus_tour.trips[trip_index]
        route = self.routes[trip.route_id]
        bus = self.buses[bus_id]
        
        print(f"\n" + "="*80)
        print(f"🚌 车辆 {bus_id} 行程 {trip_index+1} 的乘客动态变化")
        print(f"📍 线路: {trip.route_id}")
        print(f"🕐 出发时间: {trip.departure_time:.1f} 分钟")
        print(f"🎯 车辆容量: {bus.capacity} 人")
        print("="*80)
        print(f"{'站序':<4} {'站点ID':<8} {'到达时间':<10} {'上车':<6} {'下车':<6} {'当前载客':<8} {'容量利用率':<10}")
        print("-"*80)
        
        for i, stop in enumerate(route.stops):
            arrival_time = trip.arrival_times[i]
            departure_time = trip.departure_times[i]
            boarding_count = len(trip.passengers_boarded[i])
            alighting_count = len(trip.passengers_alighted[i])
            current_load = trip.passenger_load[i]
            
            # 计算容量利用率
            capacity_usage = (current_load / bus.capacity) * 100
            
            # 格式化输出
            print(f"{i+1:<4} {stop.id:<8} {arrival_time:<10.1f} {boarding_count:<6} {alighting_count:<6} "
                  f"{current_load:<8} {capacity_usage:<10.1f}%")
            
            # 检查是否超载
            if current_load > bus.capacity:
                print(f"      ⚠️  警告：超载！当前载客 {current_load} > 容量 {bus.capacity}")
            
            # 显示停站时间
            dwell_time = departure_time - arrival_time
            if dwell_time > self.base_time:
                print(f"      ⏱️  停站时间: {dwell_time:.2f} 分钟")
        
        # 行程总结
        total_boarded = sum(len(p) for p in trip.passengers_boarded)
        total_alighted = sum(len(p) for p in trip.passengers_alighted)
        max_load = max(trip.passenger_load) if trip.passenger_load else 0
        max_utilization = (max_load / bus.capacity) * 100
        
        print("-"*80)
        print(f"📊 行程总结:")
        print(f"   总上车人数: {total_boarded}")
        print(f"   总下车人数: {total_alighted}")
        print(f"   最大载客量: {max_load}/{bus.capacity} ({max_utilization:.1f}%)")
        print(f"   能耗: {trip.energy_consumed:.2f} kWh")
        print(f"   行程时长: {trip.departure_times[-1] - trip.departure_time:.1f} 分钟")
        
        # 载客量合规性检查
        overload_detected = any(load > bus.capacity for load in trip.passenger_load)
        print(f"   载客量合规: {'❌ 发现超载' if overload_detected else '✅ 符合要求'}")
        print("="*80)

    def analyze_capacity_violations(self, solution: Solution):
        """分析所有行程的载客量违规情况"""
        print(f"\n" + "="*60)
        print("📊 载客量合规性分析")
        print("="*60)
        
        total_violations = 0
        total_trips = 0
        
        for bus_tour in solution.bus_tours:
            if not bus_tour.trips:
                continue
                
            bus = self.buses[bus_tour.bus_id]
            
            for trip_idx, trip in enumerate(bus_tour.trips):
                total_trips += 1
                max_load = max(trip.passenger_load) if trip.passenger_load else 0
                
                if max_load > bus.capacity:
                    total_violations += 1
                    violation_amount = max_load - bus.capacity
                    print(f"❌ 车辆 {bus_tour.bus_id} 行程 {trip_idx+1}: 超载 {violation_amount} 人 "
                          f"(载客 {max_load}/{bus.capacity})")
        
        if total_violations == 0:
            print("✅ 所有行程均符合载客量要求")
        else:
            print(f"⚠️  发现 {total_violations}/{total_trips} 个行程存在超载")
        
        print("="*60)

    def solve_unlimited_vehicles(self) -> Solution:
        """
        求解无限车辆基准问题：
        1. 为每条线路按需分配车辆，保证20分钟发车间隔
        2. 计算所需的总车辆数量
        3. 计算总运输人数
        """
        print("开始无限车辆基准测试求解...")
        print(f"目标发车间隔: {self.departure_interval} 分钟")
        print("车辆数量: 无限制（按需分配）")
        
        # 创建一个足够大的车辆列表用于分配
        max_buses_needed = self._estimate_max_buses_needed()
        unlimited_buses = self._create_unlimited_buses(max_buses_needed)
        
        solution = Solution(unlimited_buses)
        solution.passenger_queues = copy.deepcopy(self.initial_passenger_queues)
        
        # 为每条线路按需分配车辆
        total_buses_used = 0
        route_bus_assignments = {}
        
        for route_id in self.routes.keys():
            buses_needed = self._calculate_buses_needed_for_route(route_id)
            route_buses = unlimited_buses[total_buses_used:total_buses_used + buses_needed]
            route_bus_assignments[route_id] = route_buses
            
            print(f"线路 {route_id}: 需要 {buses_needed} 辆车")
            self._schedule_unlimited_route_services(solution, route_id, route_buses)
            
            total_buses_used += buses_needed
        
        # 评估解决方案
        self._evaluate_unlimited_solution(solution, total_buses_used)
        
        return solution

    def _estimate_max_buses_needed(self) -> int:
        """估算最大可能需要的车辆数量"""
        max_buses = 0
        for route_id in self.routes.keys():
            buses_for_route = self._calculate_buses_needed_for_route(route_id)
            max_buses += buses_for_route
        
        # 增加20%的缓冲
        return int(max_buses * 1.2)

    def _create_unlimited_buses(self, max_count: int) -> List[Bus]:
        """创建无限车辆池"""
        unlimited_buses = []
        
        # 为每个车场创建足够的车辆
        depot_count = len(self.depots)
        buses_per_depot = max_count // depot_count + 1
        
        bus_id = 0
        for depot_id in range(depot_count):
            for _ in range(buses_per_depot):
                unlimited_buses.append(Bus(
                    id=bus_id, 
                    home_depot=depot_id, 
                    capacity=50,  # 与原始设置保持一致
                    max_soc=450.0, 
                    min_soc=90.0
                ))
                bus_id += 1
        
        return unlimited_buses

    def _calculate_buses_needed_for_route(self, route_id: int) -> int:
        """计算单条线路需要的车辆数量"""
        route = self.routes[route_id]
        
        # 计算单程行驶时间
        single_trip_time = self._calculate_route_duration(route)
        
        # 计算返回起点时间
        return_to_start_time = self._get_return_to_start_time(route_id)
        
        # 计算单次完整周转时间（单程 + 返回）
        total_cycle_time = single_trip_time + return_to_start_time
        
        # 计算需要的车辆数量以维持发车间隔
        buses_needed = max(1, int(np.ceil(total_cycle_time / self.departure_interval)))
        
        # 计算可能的发车次数
        max_departures = int(self.planning_horizon / self.departure_interval)
        
        # 确保有足够车辆服务整个规划期
        buses_needed_for_service = min(buses_needed, max_departures)
        
        return buses_needed_for_service

    def _schedule_unlimited_route_services(self, solution: Solution, route_id: int, assigned_buses: List[Bus]):
        """为无限车辆场景安排线路服务"""
        if not assigned_buses:
            return
        
        route = self.routes[route_id]
        
        # 计算时间参数
        single_trip_time = self._calculate_route_duration(route)
        return_to_start_time = self._get_return_to_start_time(route_id)
        
        # 生成所有发车时刻
        departure_times = []
        current_departure = 0.0
        
        while current_departure + single_trip_time <= self.planning_horizon:
            departure_times.append(current_departure)
            current_departure += self.departure_interval
        
        print(f"  线路 {route_id}: 计划 {len(departure_times)} 次发车")
        
        # 简化的轮转分配：每辆车按固定间隔发车
        for bus_idx, bus in enumerate(assigned_buses):
            bus_tour = solution.bus_tours[bus.id]
            current_soc = bus.current_soc
            
            # 该车的第一次发车时间（错开发车）
            first_departure = bus_idx * (self.departure_interval / len(assigned_buses))
            
            # 为该车安排所有发车
            bus_departure_time = first_departure
            while bus_departure_time + single_trip_time <= self.planning_horizon:
                # 检查是否需要充电
                if current_soc < bus.max_soc * self.soc_min_proportion:
                    charging_event = self._plan_charging(bus, current_soc, bus_departure_time - 30)
                    if charging_event:
                        bus_tour.charging_events.append(charging_event)
                        current_soc += charging_event.energy_added
                        bus_departure_time = max(bus_departure_time, charging_event.end_time)
                    else:
                        break  # 无法充电，停止该车服务
                
                # 创建行程
                trip = self._create_fixed_trip(route_id, bus_departure_time, bus, solution.passenger_queues)
                if trip:
                    bus_tour.trips.append(trip)
                    current_soc -= trip.energy_consumed
                    
                    # 计算下一次发车时间：当前行程结束 + 返回时间，但至少间隔departure_interval
                    trip_end_time = trip.departure_times[-1]
                    earliest_next = trip_end_time + return_to_start_time
                    scheduled_next = bus_departure_time + self.departure_interval
                    bus_departure_time = max(earliest_next, scheduled_next)
                else:
                    break
        
            # 设置最终返回时间
            if bus_tour.trips:
                last_trip = bus_tour.trips[-1]
                return_time = self._get_deadhead_time_to_depot(route_id, bus.home_depot)
                bus_tour.final_return_time = last_trip.departure_times[-1] + return_time

    def _evaluate_unlimited_solution(self, solution: Solution, total_buses_used: int):
        """评估无限车辆解决方案"""
        total_passengers = 0
        active_buses = 0
        total_trips = 0
        
        for bus_tour in solution.bus_tours:
            if len(bus_tour.trips) > 0:
                active_buses += 1
                total_trips += len(bus_tour.trips)
                
                # 统计运输的乘客数量
                for trip in bus_tour.trips:
                    for passengers_at_stop in trip.passengers_boarded:
                        total_passengers += len(passengers_at_stop)
        
        solution.total_passengers_evacuated = total_passengers
        solution.objective_value = total_passengers
        solution.penalty_value = 0.0
        solution.is_feasible = True
        
        print(f"\n无限车辆基准测试结果:")
        print(f"  总运输人数: {total_passengers}")
        print(f"  实际使用车辆数: {active_buses}")
        print(f"  预分配车辆数: {total_buses_used}")
        print(f"  总行程数: {total_trips}")
        print(f"  平均每车行程数: {total_trips/active_buses:.1f}" if active_buses > 0 else "  平均每车行程数: 0")
        print(f"  平均每车运输人数: {total_passengers/active_buses:.1f}" if active_buses > 0 else "  平均每车运输人数: 0")

    def print_unlimited_detailed_results(self, solution: Solution):
        """打印无限车辆场景的详细结果"""
        print("\n" + "="*70)
        print("无限车辆基准测试详细结果")
        print("="*70)
        
        # 按线路分组统计
        route_stats = {}
        active_buses = 0
        total_trips = 0
        total_passengers = 0
        
        for bus_tour in solution.bus_tours:
            if len(bus_tour.trips) == 0:
                continue
            
            active_buses += 1
            total_trips += len(bus_tour.trips)
            
            for trip in bus_tour.trips:
                route_id = trip.route_id
                if route_id not in route_stats:
                    route_stats[route_id] = {
                        'buses': 0,
                        'trips': 0,
                        'passengers': 0,
                        'departures': []
                    }
                
                route_stats[route_id]['trips'] += 1
                route_stats[route_id]['departures'].append(trip.departure_time)
                
                trip_passengers = sum(len(p) for p in trip.passengers_boarded)
                route_stats[route_id]['passengers'] += trip_passengers
                total_passengers += trip_passengers
            
            # 统计每条线路的车辆数
            if bus_tour.trips:
                first_route = bus_tour.trips[0].route_id
                route_stats[first_route]['buses'] += 1
        
        # 打印各线路统计
        for route_id, stats in route_stats.items():
            print(f"\n📍 线路 {route_id}:")
            print(f"  使用车辆: {stats['buses']} 辆")
            print(f"  执行行程: {stats['trips']} 次")
            print(f"  运输乘客: {stats['passengers']} 人")
            
            if stats['departures']:
                departures = sorted(stats['departures'])
                intervals = [departures[i+1] - departures[i] for i in range(len(departures)-1)]
                avg_interval = np.mean(intervals) if intervals else 0
                print(f"  平均发车间隔: {avg_interval:.1f} 分钟")
                print(f"  发车时间范围: {departures[0]:.1f} - {departures[-1]:.1f} 分钟")
        
        print(f"\n📊 系统总结:")
        print(f"  活跃车辆总数: {active_buses}")
        print(f"  服务线路数: {len(route_stats)}")
        print(f"  总行程数: {total_trips}")
        print(f"  总运输人数: {total_passengers}")
        print(f"  系统效率: {total_passengers/active_buses:.1f} 人/车" if active_buses > 0 else "  系统效率: 0 人/车")
        print("="*70)


def create_benchmark_problem(depot_info_file, route_info_file, passenger_data_file, bus_travel_speed: float = 20.0):
    """创建基准测试问题"""

    # 加载车场数据
    depot_data_list, vehicle_counts = load_depot_data_from_excel(depot_info_file)

    if depot_data_list is not None:
        depots = []
        for depot_data in depot_data_list:
            depot = Depot(
                id=depot_data['id'],
                location=depot_data['location'],
                chargers=depot_data['chargers'],
                charging_power=depot_data['charging_power']
            )
            depots.append(depot)

    # 加载路线数据
    routes_from_excel, _ = load_route_data_from_excel(route_info_file, bus_travel_speed)

    if routes_from_excel is not None:
        routes = routes_from_excel

    # 创建车辆
    buses = []
    for depot_id, num_buses in enumerate(vehicle_counts):
        for _ in range(num_buses):
            buses.append(Bus(id=len(buses), home_depot=depot_id, capacity=50, max_soc=450.0, min_soc=90.0))

    print(f"🚌 创建公交车: 总计 {len(buses)} 辆")

    # 加载乘客数据
    passenger_demand_df = load_passenger_data_from_excel(passenger_data_file)

    if passenger_demand_df is not None:
        passengers = create_passengers_from_excel_data(passenger_demand_df)
    else:
        passengers = create_passengers_random_fallback(routes)

    return buses, routes, depots, passengers


if __name__ == "__main__":
    start_time = time.time()

    print("="*70)
    print("电动公交疏散调度 - 基准测试对比")
    print("="*70)
    print("测试条件:")
    print("- 固定发车间隔: 20分钟")
    print("- 车辆固定线路运行")
    print("- 到达终点后返回起点等待")
    print("="*70)

    # 创建测试问题
    travel_speed = 20.0  # 公交行驶速度 km/h

    buses, routes, depots, passengers = create_benchmark_problem(
        depot_info_file="depot_information_random.xlsx",
        route_info_file="route_information_random.xlsx",
        passenger_data_file="passenger_demand_random.xlsx",
        bus_travel_speed=travel_speed
    )

    print(f"📋 问题规模:")
    print(f"  车场数量: {len(depots)}")
    print(f"  线路数量: {len(routes)}")
    print(f"  车辆数量: {len(buses)} (有限模式)")
    print(f"  乘客数量: {len(passengers)}")
    print(f"  规划时间: 180 分钟")

    # 初始化基准测试求解器
    benchmark_solver = ElectricBusEvacuationBench(
        buses=buses,
        routes=routes,
        depots=depots,
        passengers=passengers,
        planning_horizon=180.0  # 3小时
    )

    # 1. 有限车辆基准测试
    print("\n" + "="*70)
    print("📊 有限车辆基准测试")
    print("="*70)
    
    print("开始有限车辆求解...")
    limited_solution = benchmark_solver.solve()
    benchmark_solver.print_detailed_results(limited_solution)
    
    # 分析载客量合规性
    benchmark_solver.analyze_capacity_violations(limited_solution)
    
    # 查看第一辆车的第一个行程详情
    benchmark_solver.print_trip_passenger_dynamics(limited_solution, bus_id=0, trip_index=0)

    # 2. 无限车辆基准测试
    print("\n" + "="*70)
    print("📊 无限车辆基准测试")
    print("="*70)
    
    print("开始无限车辆求解...")
    unlimited_solution = benchmark_solver.solve_unlimited_vehicles()
    benchmark_solver.print_unlimited_detailed_results(unlimited_solution)

    # 3. 对比分析
    print("\n" + "="*70)
    print("📈 对比分析")
    print("="*70)
    
    limited_passengers = limited_solution.total_passengers_evacuated
    unlimited_passengers = unlimited_solution.total_passengers_evacuated
    
    limited_buses = sum(1 for tour in limited_solution.bus_tours if len(tour.trips) > 0)
    unlimited_buses = sum(1 for tour in unlimited_solution.bus_tours if len(tour.trips) > 0)
    
    print(f"运输能力对比:")
    print(f"  有限车辆模式: {limited_passengers} 人")
    print(f"  无限车辆模式: {unlimited_passengers} 人")
    print(f"  提升幅度: {unlimited_passengers - limited_passengers} 人 ({(unlimited_passengers/limited_passengers-1)*100:.1f}%)" if limited_passengers > 0 else "  提升幅度: 无法计算")
    
    print(f"\n车辆使用对比:")
    print(f"  有限车辆模式: {limited_buses}/{len(buses)} 辆 ({limited_buses/len(buses)*100:.1f}%)")
    print(f"  无限车辆模式: {unlimited_buses} 辆 (按需分配)")
    print(f"  额外需求: {max(0, unlimited_buses - len(buses))} 辆")
    
    print(f"\n效率对比:")
    limited_efficiency = limited_passengers / limited_buses if limited_buses > 0 else 0
    unlimited_efficiency = unlimited_passengers / unlimited_buses if unlimited_buses > 0 else 0
    print(f"  有限车辆效率: {limited_efficiency:.1f} 人/车")
    print(f"  无限车辆效率: {unlimited_efficiency:.1f} 人/车")

    end_time = time.time()
    execution_time = end_time - start_time

    print(f"\n⏱️  总执行时间: {execution_time:.2f} 秒")
    print("="*70)
